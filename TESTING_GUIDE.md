# 故事模式字幕优化测试指南

## 测试环境
- 访问地址: http://localhost:5174/AIMusicCommunity/
- 页面: AI音乐鉴赏页面

## 测试步骤

### 1. 基础功能测试

#### 1.1 页面加载
- [ ] 页面正常加载，显示音乐播放界面
- [ ] 底部字幕胶囊正常显示
- [ ] 默认显示音乐模式（蓝色图标）

#### 1.2 模式切换
- [ ] 点击字幕胶囊左侧的模式切换按钮
- [ ] 成功从音乐模式切换到故事模式（紫色图标）
- [ ] 字幕内容从歌词变为故事片段

### 2. 故事模式核心功能测试

#### 2.1 收起状态显示
- [ ] 故事模式收起状态只显示当前故事片段的前30个字符
- [ ] 显示紫色脉冲指示器
- [ ] 文字有平滑的过渡动画

#### 2.2 展开状态显示
- [ ] 点击字幕胶囊展开
- [ ] 显示"故事模式"标题和紫色图标
- [ ] 显示故事进度指示器（小圆点）
- [ ] 当前故事片段高亮显示（白色，大字体）
- [ ] 前一段和后一段弱化显示（40%透明度，小字体）
- [ ] 当前片段下方显示紫色渐变指示器

### 3. 播放同步测试

#### 3.1 播放状态
- [ ] 点击视频背景开始播放
- [ ] 故事片段随播放进度自动切换
- [ ] 切换时有微妙的缩放和透明度动画
- [ ] 进度指示器正确显示当前位置

#### 3.2 暂停状态
- [ ] 点击视频背景暂停播放
- [ ] 故事片段停止自动切换
- [ ] 保持在当前片段不变

### 4. 交互功能测试

#### 4.1 手势控制
- [ ] 在故事模式下上下滑动屏幕
- [ ] 可以手动切换故事片段
- [ ] 滑动后暂停自动播放5秒

#### 4.2 模式对比
- [ ] 切换到音乐模式
- [ ] 音乐模式显示歌词，有相同的UI结构
- [ ] 当前歌词高亮，前后歌词弱化
- [ ] 两种模式的交互体验一致

### 5. 视觉效果测试

#### 5.1 动画效果
- [ ] 故事片段切换时有平滑过渡
- [ ] 指示器有脉冲动画
- [ ] 进度点有缩放效果
- [ ] 所有动画时长约300-500ms

#### 5.2 视觉层次
- [ ] 当前内容明显突出
- [ ] 上下文内容适当弱化
- [ ] 颜色搭配协调（紫色系）
- [ ] 字体大小层次清晰

### 6. 边界情况测试

#### 6.1 第一个片段
- [ ] 播放开始时显示第一个故事片段
- [ ] 没有"前一段"内容
- [ ] 进度指示器正确显示

#### 6.2 最后一个片段
- [ ] 播放到最后显示最后一个故事片段
- [ ] 没有"后一段"内容
- [ ] 进度指示器正确显示

#### 6.3 快速切换
- [ ] 快速点击模式切换按钮
- [ ] 界面响应流畅，无卡顿
- [ ] 状态切换正确

## 预期结果

### ✅ 成功标准
1. 故事模式字幕显示方式与音乐模式完全一致
2. 只高亮当前播放的故事片段，弱化其他内容
3. 只在视频播放时故事内容才跟随滚动
4. 所有动画效果流畅自然
5. 交互体验直观易用

### ❌ 失败标准
1. 故事模式仍显示大段文字
2. 没有高亮当前内容
3. 暂停时故事仍在自动切换
4. 动画卡顿或缺失
5. 模式切换异常

## 问题报告
如发现问题，请记录：
- 问题描述
- 复现步骤
- 预期行为
- 实际行为
- 浏览器信息

## 优化建议
测试完成后，可以考虑的进一步优化：
- 调整故事片段的时间间隔
- 优化动画效果的时长和缓动
- 增加更多视觉反馈
- 改进进度指示器的样式
