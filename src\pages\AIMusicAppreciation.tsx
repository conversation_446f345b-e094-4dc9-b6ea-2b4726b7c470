import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  ArrowLeft, Play, Pause, Heart, MessageCircle, Share2,
  Sparkles, ChevronUp, X, Volume2, SkipForward, SkipBack,
  Star, Bookmark, Headphones, Wand2, Flame, Music2,
  Eye, TrendingUp, Zap, Edit3, Plus, ChevronDown,
  Users, Clock, Hash, Mic, Radio, Layers, MoreVertical, ArrowDown, Flag,
  BookOpen, Check, Shuffle, Repeat, List
} from 'lucide-react';
import AIChat from '../components/AIChat';

// 开发环境使用相对路径，生产环境使用BASE_URL
const videoUrl = `${import.meta.env.BASE_URL}videos/MusicStory.mp4`;

// ==================== 类型定义 ====================
type MusicMode = 'foreground' | 'background';

interface Song {
  id: string;
  title: string;
  artist: string;
  coverUrl: string;
  duration: number;
  currentTime: number;
  mood: string;
  moodEmoji: string;
  story: StorySegment[];
  remixVersions: RemixVersion[];
  stats: {
    plays: number;
    likes: number;
    remixes: number;
    resonance: number;
  };
}

interface StorySegment {
  id: string;
  type: 'creation' | 'emotion' | 'inspiration' | 'memory';
  title: string;
  content: string;
  timestamp?: string;
  highlight?: boolean;
  visual?: string;
}

interface RemixVersion {
  id: string;
  label: string;
  emoji: string;
  description: string;
  intensity: 'light' | 'medium' | 'heavy';
  applied?: boolean;
}

interface DanmakuComment {
  id: string;
  text: string;
  userName: string;
  position: number;
  speed: number;
  color?: string;
  size?: 'small' | 'medium' | 'large';
}

interface RemixSong {
  id: string;
  title: string;
  artist: string;
  originalSongId: string;
  duration: number;
  plays: number;
  likes: number;
  createdAt: Date;
  remixType: 'lyrics' | 'vocal' | 'rhythm' | 'style' | 'instrument' | 'mixed';
  remixIntensity: 'light' | 'medium' | 'heavy';
  remixDescription: string;
  highlight: string;
  coverUrl?: string;
  tags: string[];
}

// ==================== 模拟数据 ====================
const mockSong: Song = {
  id: 'song-1',
  title: '夏日晚风',
  artist: '@月光诗人',
  coverUrl: videoUrl,
  duration: 245,
  currentTime: 0,
  mood: 'romantic',
  moodEmoji: '💕',
  story: [
    {
      id: 's1',
      type: 'creation',
      title: '创作故事',
      content: '这首歌诞生在一个夏天的傍晚，微风轻拂，夕阳西下。创作者坐在海边，看着远方的地平线，心中涌起了对生活的无限感慨。\n\n那是一个特别的日子，天空染上了金色的光辉，海浪轻柔地拍打着岸边，仿佛在诉说着什么古老的秘密。空气中弥漫着咸咸的海风，带着一丝丝的凉意，让人感到无比的宁静与祥和。\n\n创作者想起了自己的童年，想起了那些在海边度过的美好时光。记得那时候，总是喜欢赤着脚在沙滩上奔跑，感受着细沙从脚趾间流过的感觉。那时候的梦想很简单，就是希望能够永远留在这样的时光里。\n\n随着夜幕的降临，星星一颗颗地出现在天空中，像是无数双眼睛在注视着这个世界。创作者突然意识到，生活中的每一个瞬间都是如此珍贵，无论是快乐还是悲伤，都是构成我们生命的重要部分。\n\n于是，他拿起吉他，开始弹奏起这段旋律。音符像海浪一样涌动，像晚风一样轻柔，像星光一样闪烁。这首歌就这样诞生了，带着对生活的热爱，对时光的珍惜，对未来的期许。',
      timestamp: '2025.09.18 周五 上海 · 天气晴朗',
      highlight: true,
      visual: 'sunset'
    }
  ],
  remixVersions: [
    { id: 'r1', label: '深夜疗愈', emoji: '🌙', description: '更柔和的编曲，适合深夜独处', intensity: 'light' },
    { id: 'r2', label: '元气清晨', emoji: '☀️', description: '节奏轻快，唤醒美好一天', intensity: 'medium' },
    { id: 'r3', label: '专注工作', emoji: '💻', description: '去除人声，纯音乐循环', intensity: 'light' },
    { id: 'r4', label: '运动节拍', emoji: '🏃', description: 'BPM提升，激发运动潜能', intensity: 'heavy' },
    { id: 'r5', label: '伤感深情', emoji: '💔', description: '小调编曲，情感更加深沉', intensity: 'medium' },
    { id: 'r6', label: '浪漫约会', emoji: '💕', description: '添加小提琴，营造浪漫氛围', intensity: 'light' }
  ],
  stats: {
    plays: 125800,
    likes: 34210,
    remixes: 892,
    resonance: 8921
  }
};

const mockDanmakus: DanmakuComment[] = [
  { id: 'd1', text: '每次听都想哭 😢', userName: '深夜听歌人', position: 10, speed: 20, size: 'medium' },
  { id: 'd2', text: '这就是夏天的感觉啊', userName: '夏日微风', position: 35, speed: 18, color: '#FFD700', size: 'medium' },
  { id: 'd3', text: '想起了那个夏天的TA', userName: '回忆收藏家', position: 60, speed: 22, size: 'small' },
  { id: 'd4', text: '单曲循环第100遍！', userName: '音乐狂热者', position: 85, speed: 19, color: '#FF69B4', size: 'medium' },
  { id: 'd5', text: '前奏就泪目了...', userName: '感性听众', position: 25, speed: 21, size: 'medium', color: '#87CEEB' },
  { id: 'd6', text: '🎵🎵🎵', userName: '音符精灵', position: 50, speed: 17, size: 'large' },
  { id: 'd7', text: '深夜EMO必备', userName: '夜猫子', position: 75, speed: 24, size: 'small', color: '#DDA0DD' },
  { id: 'd8', text: '这旋律太治愈了', userName: '音乐疗愈师', position: 45, speed: 20, size: 'medium', color: '#98FB98' },
];

const lyrics = [
  { time: 0, text: '当夏日的晚风轻轻吹过' },
  { time: 4, text: '带走了白天所有的燥热' },
  { time: 8, text: '我站在这里静静等候' },
  { time: 12, text: '等待着你出现的时刻' },
  { time: 16, text: '♪ ♪ ♪' },
  { time: 20, text: '海浪拍打着礁石歌唱' },
  { time: 24, text: '诉说着古老的故事' },
  { time: 28, text: '星星开始在天空闪烁' },
  { time: 32, text: '照亮了回家的路' },
];

// 故事片段数据 - 按时间轴分段，与歌词同步
const storySegments = [
  { time: 0, text: '这首歌诞生在一个夏天的傍晚，微风轻拂，夕阳西下。' },
  { time: 4, text: '创作者坐在海边，看着远方的地平线，心中涌起了对生活的无限感慨。' },
  { time: 8, text: '那是一个特别的日子，天空染上了金色的光辉。' },
  { time: 12, text: '海浪轻柔地拍打着岸边，仿佛在诉说着什么古老的秘密。' },
  { time: 16, text: '空气中弥漫着咸咸的海风，带着一丝丝的凉意。' },
  { time: 20, text: '创作者想起了自己的童年，想起了那些在海边度过的美好时光。' },
  { time: 24, text: '记得那时候，总是喜欢赤着脚在沙滩上奔跑。' },
  { time: 28, text: '随着夜幕的降临，星星一颗颗地出现在天空中。' },
  { time: 32, text: '于是，他拿起吉他，开始弹奏起这段旋律。' },
];

const mockRemixSongs: RemixSong[] = [
  {
    id: 'remix-1',
    title: '夏日晚风 · 爵士夜语',
    artist: '爵士猫咪',
    originalSongId: 'song-1',
    duration: 268,
    plays: 8420,
    likes: 1892,
    createdAt: new Date('2024-08-18'),
    remixType: 'style',
    remixIntensity: 'medium',
    remixDescription: '爵士风格重编',
    highlight: '萨克斯独奏超燃',
    tags: ['爵士', '深夜', '萨克斯']
  },
  {
    id: 'remix-2', 
    title: '夏日晚风 · 电音狂潮',
    artist: 'EDM小王子',
    originalSongId: 'song-1',
    duration: 195,
    plays: 15670,
    likes: 3421,
    createdAt: new Date('2024-08-19'),
    remixType: 'mixed',
    remixIntensity: 'heavy',
    remixDescription: '电音+说唱混搭',
    highlight: 'Drop炸裂全场',
    tags: ['电音', 'EDM', '说唱']
  },
  {
    id: 'remix-3',
    title: '夏日晚风 · 古风吟',
    artist: '墨染青衣',
    originalSongId: 'song-1', 
    duration: 312,
    plays: 6890,
    likes: 2156,
    createdAt: new Date('2024-08-20'),
    remixType: 'lyrics',
    remixIntensity: 'light',
    remixDescription: '古风歌词重写',
    highlight: '诗词韵律绝美',
    tags: ['古风', '诗词', '国风']
  },
  {
    id: 'remix-4',
    title: '夏日晚风 · 摇滚版',
    artist: '雷电乐队',
    originalSongId: 'song-1',
    duration: 234,
    plays: 12340,
    likes: 2890,
    createdAt: new Date('2024-08-21'),
    remixType: 'instrument',
    remixIntensity: 'heavy',
    remixDescription: '摇滚乐器编配',
    highlight: '吉他Solo燃爆',
    tags: ['摇滚', '电吉他', '激情']
  }
];

// ==================== 主组件 ====================
const MusicAppreciationPage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentSong] = useState<Song>(mockSong || {
    id: 'default',
    title: '默认歌曲',
    artist: '未知艺术家',
    coverUrl: '',
    duration: 0,
    currentTime: 0,
    mood: 'calm',
    moodEmoji: '🎵',
    story: [],
    remixVersions: [],
    stats: { plays: 0, likes: 0, remixes: 0, resonance: 0 }
  });
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [currentLyricIndex, setCurrentLyricIndex] = useState(0);
  const [currentStorySegmentIndex, setCurrentStorySegmentIndex] = useState(0);
  const [storySegmentChanging, setStorySegmentChanging] = useState(false);
  const [showAllComments, setShowAllComments] = useState(false);
  const [showRemixPanel, setShowRemixPanel] = useState(false);
  const [selectedRemix, setSelectedRemix] = useState<RemixVersion | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [danmakus, setDanmakus] = useState<DanmakuComment[]>(mockDanmakus || []);
  const [showDanmaku, setShowDanmaku] = useState(true);
  const [aiVisible, setAiVisible] = useState(true);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [autoPlayStory, setAutoPlayStory] = useState(true);
  const [remixModalTab, setRemixModalTab] = useState<'quick' | 'creative'>('quick');
  const [remixSongsList] = useState<RemixSong[]>(mockRemixSongs || []);
  const [showPlaylist, setShowPlaylist] = useState(false);
  const [playlistMode, setPlaylistMode] = useState<'normal' | 'story'>('normal');
  const [currentStoryId] = useState('story-westjourney');
  const [currentStoryChapter, setCurrentStoryChapter] = useState(2);
  const [playMode, setPlayMode] = useState<'sequence' | 'shuffle' | 'repeat'>('sequence');
  const [aiContinuePlay, setAiContinuePlay] = useState(true);
  const [currentSongIndex, setCurrentSongIndex] = useState(0);
  const [defaultMusicMode, setDefaultMusicMode] = useState<MusicMode>('foreground');
  
  // AI界面状态 - 统一管理
  const [aiUIState, setAiUIState] = useState({
    showRibbon: true,      // AI丝带显示状态
    showChat: false,       // AI聊天蒙层显示状态
    ribbonVisible: true    // 丝带内容是否可见
  });

  // AI状态管理辅助函数
  const openAiChat = useCallback(() => {
    setAiUIState(prev => ({
      ...prev,
      showChat: true,
      showRibbon: false  // 打开聊天时隐藏丝带
    }));
  }, []);

  const closeAiChat = useCallback(() => {
    setAiUIState(prev => ({
      ...prev,
      showChat: false,
      showRibbon: true,  // 关闭聊天时显示丝带
      ribbonVisible: true
    }));
  }, []);

  const toggleAiRibbon = useCallback(() => {
    setAiUIState(prev => ({
      ...prev,
      ribbonVisible: !prev.ribbonVisible
    }));
  }, []);
  
  // 统一的AI消息状态管理
  const [aiState, setAiState] = useState<{
    currentMessage: string;
    displayMessage: string;
    mood: 'happy' | 'excited' | 'calm' | 'curious' | 'encouraging';
    suggestions?: string[];
    isTyping: boolean;
  }>({
    currentMessage: '欢迎来到音乐的世界！双击你喜欢的歌词，记录此刻的心情 ✨',
    displayMessage: '',
    mood: 'calm',
    suggestions: ['改为欢快版本', '歌曲鉴赏', '修改歌词'],
    isTyping: false
  });



  // 获取二创类型标签
  const getRemixTypeLabel = (type: string) => {
    switch (type) {
      case 'lyrics': return '歌词改编';
      case 'vocal': return '演唱重塑';
      case 'rhythm': return '节拍重构';
      case 'style': return '风格转换';
      case 'instrument': return '乐器编配';
      case 'mixed': return '混合改编';
      default: return '其他改编';
    }
  };

  // 获取改编强度颜色
  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'light': return 'from-green-400 to-emerald-400';
      case 'medium': return 'from-yellow-400 to-orange-400';
      case 'heavy': return 'from-red-400 to-pink-400';
      default: return 'from-gray-400 to-slate-400';
    }
  };

  // 获取二创类型图标
  const getRemixTypeIcon = (type: string) => {
    switch (type) {
      case 'lyrics': return Edit3;
      case 'vocal': return Mic;
      case 'rhythm': return Music2;
      case 'style': return Wand2;
      case 'instrument': return Headphones;
      case 'mixed': return Layers;
      default: return Music2;
    }
  };

  // 播放列表数据
  const [playlist] = useState<Song[]>([
    mockSong,
    {
      id: 'song-2',
      title: '星空下的约定',
      artist: '梦境歌者',
      coverUrl: videoUrl,
      duration: 198,
      currentTime: 0,
      mood: 'dreamy',
      moodEmoji: '✨',
      story: [],
      remixVersions: [],
      stats: { plays: 8920, likes: 2341, remixes: 567, resonance: 5678 }
    },
    {
      id: 'song-3',
      title: '雨后彩虹',
      artist: '晴天音乐人',
      coverUrl: videoUrl,
      duration: 212,
      currentTime: 0,
      mood: 'hopeful',
      moodEmoji: '🌈',
      story: [],
      remixVersions: [],
      stats: { plays: 15670, likes: 4521, remixes: 1234, resonance: 9876 }
    }
  ]);

  // 故事数据
  const currentStoryData = {
    id: 'story-westjourney',
    title: '西游新篇',
    description: '用现代音乐重新演绎经典西游记，每一章都是一段奇幻冒险',
    totalChapters: 10,
    author: '幻音工作室'
  };

  const storyPlaylistData = [
    {
      id: 'story-ch1',
      title: '花果山的觉醒',
      subtitle: '石猴出世，初遇天地灵气',
      duration: 245,
      played: true
    },
    {
      id: 'story-ch2',
      title: '龙宫借宝',
      subtitle: '深海龙宫，定海神针的传说',
      duration: 312,
      played: true
    },
    {
      id: 'story-ch3',
      title: '大闹天宫',
      subtitle: '齐天大圣的怒吼震动三界',
      duration: 420,
      played: false
    }
  ];

  const [storyData] = useState(currentStoryData);
  const [storyPlaylist] = useState(storyPlaylistData);
  
  // 新增状态
  const [resonanceAnimation, setResonanceAnimation] = useState(false);
  const [followAnimation, setFollowAnimation] = useState(false);
  const [contentMode, setContentMode] = useState<'story' | 'lyrics'>('story');
  const [isImmersiveMode, setIsImmersiveMode] = useState(false);

  // AI音律球相关状态
  const [aiOrbAnimation, setAiOrbAnimation] = useState(false);
  const aiOrbClickTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 新增：前景音/背景音模式状态
  const [musicMode, setMusicMode] = useState<MusicMode>('foreground');
  const [showSubtitles, setShowSubtitles] = useState(true);
  const [subtitleExpanded, setSubtitleExpanded] = useState(false);
  
  // 音乐模式切换函数
  const toggleMusicMode = useCallback(() => {
    setMusicMode(prev => prev === 'foreground' ? 'background' : 'foreground');
  }, []);
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const storyTimer = useRef<NodeJS.Timeout | null>(null);
  const touchStartY = useRef(0);
  const touchStartX = useRef(0);
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null); 

  // ========== 全局错误处理 ==========
  useEffect(() => {
    // 保存原始的 Object.keys 方法
    const originalObjectKeys = Object.keys;
    
    // 重写 Object.keys 方法以防止 null/undefined 错误
    Object.keys = function(obj: any): string[] {
      if (obj === null || obj === undefined) {
        console.warn('Object.keys() 接收到 null 或 undefined，返回空数组');
        return [];
      }
      return originalObjectKeys.call(this, obj);
    };

    // 捕获全局错误，特别是 Object.keys() 相关错误
    const handleGlobalError = (event: ErrorEvent) => {
      if (event.message && event.message.includes('Cannot convert undefined or null to object')) {
        console.warn('捕获到 Object.keys() 错误，已阻止:', event.message);
        event.preventDefault();
        return false;
      }
    };

    // 捕获未处理的 Promise 错误
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason && event.reason.message && event.reason.message.includes('Cannot convert undefined or null to object')) {
        console.warn('捕获到 Promise 中的 Object.keys() 错误，已阻止:', event.reason.message);
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      // 恢复原始的 Object.keys 方法
      Object.keys = originalObjectKeys;
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // ========== 组件卸载清理 ==========
  useEffect(() => {
    return () => {
      // 清理所有定时器，防止内存泄漏
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
      }
      if (storyTimer.current) {
        clearInterval(storyTimer.current);
      }
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
      }
      if (aiOrbClickTimerRef.current) {
        clearTimeout(aiOrbClickTimerRef.current);
        aiOrbClickTimerRef.current = null;
      }
    };
  }, []);

  // ========== 播放控制 ==========
  const startPlayback = useCallback(() => {
    if (playbackTimer.current) clearInterval(playbackTimer.current);
    
    playbackTimer.current = setInterval(() => {
      setCurrentTime(prev => {
        const newTime = prev + 0.1;
        if (newTime >= currentSong.duration) {
          setIsPlaying(false);
          return 0;
        }
        
        // 更新歌词
        const currentLyric = lyrics.find((_, index) => {
          const nextLyric = lyrics[index + 1];
          return newTime >= _.time && (!nextLyric || newTime < nextLyric.time);
        });
        if (currentLyric) {
          setCurrentLyricIndex(lyrics.indexOf(currentLyric));
        }

        // 更新故事片段
        const currentStorySegment = storySegments.find((_, index) => {
          const nextSegment = storySegments[index + 1];
          return newTime >= _.time && (!nextSegment || newTime < nextSegment.time);
        });
        if (currentStorySegment) {
          const newIndex = storySegments.indexOf(currentStorySegment);
          if (newIndex !== currentStorySegmentIndex) {
            setStorySegmentChanging(true);
            setCurrentStorySegmentIndex(newIndex);
            setTimeout(() => setStorySegmentChanging(false), 300);
          }
        }
        
        return newTime;
      });
    }, 100);
  }, [currentSong.duration]);

  // ========== 视频背景控制 ==========
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // 设置视频初始状态
    if (isPlaying) {
      // 播放视频
      const playPromise = video.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.log('视频自动播放失败:', error);
          // 浏览器阻止自动播放时，添加用户交互提示
          setAiState(prev => ({
            ...prev,
            currentMessage: '点击屏幕任意位置开始播放视频背景 🎬',
            mood: 'encouraging'
          }));
        });
      }
    } else {
      // 暂停视频并显示第一帧
      video.pause();
      // 延迟设置currentTime，确保视频已暂停
      setTimeout(() => {
        if (video && !isPlaying) {
          video.currentTime = 0;
        }
      }, 100);
    }

    // 确保视频加载完成后的处理
    const handleLoadedMetadata = () => {
      console.log('视频元数据加载完成');
      if (!isPlaying) {
        video.currentTime = 0; // 确保显示第一帧
      }
    };

    const handleLoadedData = () => {
      console.log('视频数据加载完成');
      if (!isPlaying) {
        video.currentTime = 0;
      }
    };

    const handleError = (e: Event) => {
      console.error('视频加载错误:', e);
      console.error('视频源路径:', videoUrl);
      console.error('视频元素:', video);
      if (video) {
        console.error('视频错误详情:', video.error);
      }
      setAiState(prev => ({
        ...prev,
        currentMessage: '视频加载失败，请检查网络连接 😔',
        mood: 'encouraging'
      }));
    };

    const handleCanPlay = () => {
      // console.log('视频可以播放');
      // console.log('视频路径:', videoUrl);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
    };
  }, [isPlaying]);

  useEffect(() => {
    if (isPlaying) {
      startPlayback();
    } else if (playbackTimer.current) {
      clearInterval(playbackTimer.current);
    }

    return () => {
      if (playbackTimer.current) clearInterval(playbackTimer.current);
    };
  }, [isPlaying, startPlayback]);
  
  // 优化的打字机效果
  const typingTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    if (aiState.currentMessage && !aiState.isTyping) {
      setAiState(prev => ({ ...prev, displayMessage: '', isTyping: true }));
      
      let index = 0;
      const text = aiState.currentMessage;
      
      const typeChar = () => {
        if (index < text.length) {
          setAiState(prev => ({
            ...prev,
            displayMessage: text.substring(0, index + 1)
          }));
          index++;
          typingTimerRef.current = setTimeout(typeChar, 30);
        } else {
          setAiState(prev => ({ ...prev, isTyping: false }));
        }
      };
      
      typingTimerRef.current = setTimeout(typeChar, 300);
    }
    
    return () => {
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current);
        typingTimerRef.current = null;
      }
    };
  }, [aiState.currentMessage]);

  // ========== 初始化AI消息 ==========
  useEffect(() => {
    // 组件挂载时触发初始消息显示
    if (aiState.currentMessage && !aiState.displayMessage && !aiState.isTyping) {
      setAiState(prev => ({ ...prev, isTyping: false })); // 触发打字机效果
    }
  }, []);

  // ========== 视频初始化 ==========
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      // 确保视频加载后显示第一帧
      const handleCanPlayThrough = () => {
        if (!isPlaying) {
          video.currentTime = 0;
        }
      };
      
      video.addEventListener('canplaythrough', handleCanPlayThrough);
      
      // 如果视频已经可以播放，立即设置第一帧
      if (video.readyState >= 3) {
        handleCanPlayThrough();
      }
      
      return () => {
        video.removeEventListener('canplaythrough', handleCanPlayThrough);
      };
    }
  }, [isPlaying]);

  // ========== 故事自动播放 - 只有在视频播放时才自动切换 ==========
  useEffect(() => {
    if (autoPlayStory && contentMode === 'story' && storySegments.length > 1 && isPlaying) {
      storyTimer.current = setInterval(() => {
        setCurrentStorySegmentIndex(prev =>
          prev < storySegments.length - 1 ? prev + 1 : 0
        );
      }, 8000);
    }

    return () => {
      if (storyTimer.current) clearInterval(storyTimer.current);
    };
  }, [autoPlayStory, contentMode, storySegments.length, isPlaying]);

  // ========== 手势处理 ==========
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY;
    touchStartX.current = e.touches[0].clientX;
    setAutoPlayStory(false);
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const deltaY = touchStartY.current - e.changedTouches[0].clientY;
    const deltaX = touchStartX.current - e.changedTouches[0].clientX;
    
    // 左右滑动切换故事/歌词模式
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      setContentMode(prev => prev === 'story' ? 'lyrics' : 'story');
    }
    // 上下滑动切换故事段落
    else if (Math.abs(deltaY) > 50 && contentMode === 'story') {
      if (deltaY > 0) {
        setCurrentStorySegmentIndex(prev =>
          prev < storySegments.length - 1 ? prev + 1 : 0
        );
      } else {
        setCurrentStorySegmentIndex(prev =>
          prev > 0 ? prev - 1 : storySegments.length - 1
        );
      }
    }
    
    setTimeout(() => setAutoPlayStory(true), 5000);
  }, [contentMode, currentSong.story.length]);

  // ========== 快速二创 ==========
  const handleQuickRemix = useCallback((remix: RemixVersion) => {
    setSelectedRemix(remix);
    setAiState({
      currentMessage: `正在为你生成${remix.label}版本... ${remix.emoji}`,
      displayMessage: '',
      mood: 'excited',
      suggestions: [],
      isTyping: false
    });
    
    setTimeout(() => {
      setAiState({
        currentMessage: `${remix.label}版本已就绪！${remix.description}`,
        displayMessage: '',
        mood: 'happy',
        suggestions: ['立即播放', '继续调整', '分享给朋友'],
        isTyping: false
      });
    }, 2000);
  }, []);

  // ========== 共鸣动画 ==========
  const handleResonance = useCallback(() => {
    setIsLiked(!isLiked);
    setResonanceAnimation(true);
    setTimeout(() => setResonanceAnimation(false), 1000);
  }, [isLiked]);

  // ========== 关注功能 ==========
  const handleFollow = useCallback(() => {
    const newFollowState = !isFollowing;
    setIsFollowing(newFollowState);
    setFollowAnimation(true);
    
    // 显示AI反馈消息
    setAiState({
      currentMessage: newFollowState ? `已关注 ${currentSong.artist}，将会第一时间收到TA的新作品通知 ✨` : `已取消关注 ${currentSong.artist}`,
      displayMessage: '',
      mood: newFollowState ? 'happy' : 'calm',
      suggestions: newFollowState ? ['查看作品列表', '设置特别关注', '分享给朋友'] : [],
      isTyping: false
    });
    
    setTimeout(() => setFollowAnimation(false), 800);
  }, [isFollowing, currentSong.artist]);

  // ========== 视频播放控制 ==========
  const handleBackgroundClick = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      // 当前正在播放，点击暂停
      video.pause();
      setIsPlaying(false);
    } else {
      // 当前暂停，点击播放
      video.play().catch(error => {
        console.log('视频播放失败:', error);
      });
      setIsPlaying(true);
    }
  }, [isPlaying]);

  // ========== 弹幕组件 - 横向滚动效果 ==========
  const DanmakuLayer = () => {
    const [animationKey, setAnimationKey] = useState(0);
    
    // 当弹幕显示状态改变时重置动画
    useEffect(() => {
      if (showDanmaku) {
        setAnimationKey(prev => prev + 1);
      }
    }, [showDanmaku]);
    
    return (
      <div className={`absolute top-36 left-0 right-0 h-20 pointer-events-none z-40 overflow-hidden ${showDanmaku ? '' : 'hidden'}`}>
        {danmakus.map((danmaku, index) => {
          // 使用固定的延迟时间，避免重新渲染时重置动画
          const delay = (index * 3) % 20; // 循环延迟，避免所有弹幕同时出现
          return (
            <div
              key={`${danmaku.id}-${animationKey}`} // 添加animationKey确保动画重置
              className="absolute whitespace-nowrap"
              style={{
                top: `${danmaku.position}%`,
                left: '100%',
                animation: `danmaku-scroll ${danmaku.speed}s ${delay}s linear infinite`,
                fontSize: danmaku.size === 'large' ? '16px' : danmaku.size === 'small' ? '12px' : '14px',
                color: danmaku.color || 'rgba(255, 255, 255, 0.9)',
                textShadow: '0 2px 4px rgba(0,0,0,0.9)',
                fontWeight: '500'
              }}
            >
              {danmaku.text}
            </div>
          );
        })}
      </div>
    );
  };

  // ========== 渲染方法 ==========
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 10000) return `${(num / 10000).toFixed(1)}w`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
    return num.toString();
  };

  const currentLyric = lyrics[currentLyricIndex];
  const currentStorySegment = storySegments[currentStorySegmentIndex];

  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div 
          ref={containerRef}
          className="relative w-full h-full bg-black rounded-[2.8rem] overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {/* 全屏背景视频 */}
          <div className="absolute inset-0" onClick={handleBackgroundClick}>
            <video
              ref={videoRef}
              src={videoUrl}
              className="w-full h-full object-cover cursor-pointer"
              loop
              muted
              playsInline
              preload="metadata"
              poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1920 1080'%3E%3Cdefs%3E%3ClinearGradient id='bg' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%23667eea;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%23764ba2;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23bg)' /%3E%3C/svg%3E"
              onLoadedData={() => {
                // 视频加载完成后确保显示第一帧
                if (videoRef.current && !isPlaying) {
                  videoRef.current.currentTime = 0;
                }
              }}
              onCanPlay={() => {
                // 视频可以播放时的处理
                if (videoRef.current && !isPlaying) {
                  videoRef.current.currentTime = 0;
                }
              }}
            />
            {/* 渐变遮罩 - 确保内容可读性 */}
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/90 pointer-events-none" />
            
            {/* 动态粒子效果 */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {Array.from({ length: 20 }).map((_, i) => (
                <div
                  key={i}
                  className="absolute w-0.5 h-0.5 bg-white rounded-full animate-float-particle"
                  style={{
                    left: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 10}s`,
                    animationDuration: `${15 + Math.random() * 15}s`,
                    opacity: Math.random() * 0.4 + 0.1
                  }}
                />
              ))}
            </div>
          </div>

          {/* 弹幕层 - 横向滚动 */}
          {!isImmersiveMode && <DanmakuLayer />}
          
          {/* 弹幕动画样式 */}
          <style>{`
          @keyframes danmaku-scroll {
            from {
              transform: translateX(0);
            }
            to {
              transform: translateX(calc(-100vw - 200px));
            }
          }
          `}</style>

          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <span className="font-medium">9:41</span>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="w-4 h-4 border border-white/50 rounded-sm">
                <div className="h-full bg-white rounded-sm" style={{ width: '70%' }} />
              </div>
            </div>
          </div>


          {/* 字幕区域 - 轻量化悬浮胶囊设计 */}
          {!isImmersiveMode && showSubtitles && (
            <div className="absolute inset-x-0 bottom-48 px-8 transition-all duration-300">
              {/* 悬浮胶囊字幕 */}
              <div 
                className={`mx-auto max-w-sm bg-black/20 backdrop-blur-lg rounded-full border border-white/5 transition-all duration-500 cursor-pointer hover:bg-black/30 ${
                  subtitleExpanded ? 'rounded-2xl px-6 py-4 max-w-md' : 'px-4 py-2'
                }`}
                onClick={() => setSubtitleExpanded(!subtitleExpanded)}
              >
                {/* 字幕内容 */}
                <div className="text-center relative">
                  {musicMode === 'background' ? (
                    // 故事字幕模式
                    <div className="animate-fade-in">
                      {!subtitleExpanded ? (
                        // 收起状态 - 只显示一行核心内容
                        <div className="flex items-center gap-2">
                          {/* 模式切换按钮 - 固定在左侧 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleMusicMode();
                            }}
                            className="w-5 h-5 rounded-full bg-gradient-to-r from-purple-500/60 to-pink-500/60 hover:from-purple-500/80 hover:to-pink-500/80 flex items-center justify-center transition-all hover:scale-110 flex-shrink-0"
                            title="切换到音乐模式"
                          >
                            <BookOpen className="w-2.5 h-2.5 text-white" />
                          </button>
                          <div className="w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" />
                          <p className="text-white text-sm font-medium truncate flex-1 transition-all duration-300">
                            {storySegments[currentStorySegmentIndex]?.text.substring(0, 30)}...
                          </p>
                          <ChevronDown className="w-3 h-3 text-white/60 flex-shrink-0" />
                        </div>
                      ) : (
                        // 展开状态 - 显示详细内容
                        <div>
                          {/* 增强的标题栏 */}
                          <div className="flex items-center justify-between mb-3 pb-2 border-b border-white/10">
                            <div className="flex items-center gap-2">
                              {/* 放大的模式图标 */}
                              <div className="w-6 h-6 rounded-lg bg-gradient-to-r from-purple-500/40 to-pink-500/40 flex items-center justify-center">
                                <BookOpen className="w-4 h-4 text-white" />
                              </div>
                              {/* 更明显的标题 */}
                              <span className="text-white text-base font-semibold">故事模式</span>
                              {/* 故事进度指示器 */}
                              <div className="flex items-center gap-1 ml-2">
                                {storySegments.map((_, index) => (
                                  <div
                                    key={index}
                                    className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                                      index === currentStorySegmentIndex
                                        ? 'bg-purple-400 scale-125'
                                        : index < currentStorySegmentIndex
                                        ? 'bg-purple-400/60'
                                        : 'bg-white/20'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                            
                            {/* 右侧按钮组 */}
                            <div className="flex items-center gap-2">
                              {/* 设为默认按钮 */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDefaultMusicMode('background');
                                  // 显示反馈
                                  setAiState({
                                    currentMessage: '已设置默认播放故事模式 📖',
                                    displayMessage: '',
                                    mood: 'happy',
                                    suggestions: [],
                                    isTyping: false
                                  });
                                }}
                                className={`flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium transition-all ${
                                  defaultMusicMode === 'background'
                                    ? 'bg-purple-500/30 text-purple-300 border border-purple-400/50'
                                    : 'bg-white/10 text-white/60 hover:bg-white/15 hover:text-white/80'
                                }`}
                              >
                                <Check className="w-3 h-3" />
                                <span>默认</span>
                              </button>
                              
                              {/* 模式切换按钮 */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleMusicMode();
                                }}
                                className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500/60 to-cyan-500/60 hover:from-blue-500/80 hover:to-cyan-500/80 flex items-center justify-center transition-all hover:scale-110"
                                title="切换到音乐模式"
                              >
                                <Music2 className="w-3 h-3 text-white" />
                              </button>
                              
                              <ChevronUp className="w-3 h-3 text-white/60 flex-shrink-0" />
                            </div>
                          </div>
                          
                          {/* 故事内容 - 仿照音乐模式的前后文显示 */}
                          <div className="space-y-3">
                            {/* 前一段 - 弱化显示 */}
                            {currentStorySegmentIndex > 0 && (
                              <div className="transition-all duration-500 ease-in-out">
                                <p className="text-white/40 text-sm leading-relaxed">
                                  {storySegments[currentStorySegmentIndex - 1].text}
                                </p>
                              </div>
                            )}

                            {/* 当前段 - 高亮显示 */}
                            <div className={`transition-all duration-500 ease-in-out transform ${
                              storySegmentChanging ? 'scale-105 opacity-80' : 'scale-100 opacity-100'
                            }`}>
                              <p className="text-white text-lg font-medium leading-relaxed animate-fade-in">
                                {storySegments[currentStorySegmentIndex]?.text || '故事正在展开...'}
                              </p>
                              {/* 添加一个微妙的高亮指示器 */}
                              <div className={`w-8 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mt-2 transition-all duration-300 ${
                                storySegmentChanging ? 'animate-pulse scale-110' : 'animate-pulse'
                              }`} />
                            </div>

                            {/* 后一段 - 弱化显示 */}
                            {currentStorySegmentIndex < storySegments.length - 1 && (
                              <div className="transition-all duration-500 ease-in-out">
                                <p className="text-white/40 text-sm leading-relaxed">
                                  {storySegments[currentStorySegmentIndex + 1].text}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    // 音乐歌词模式
                    <div className="animate-fade-in">
                      {!subtitleExpanded ? (
                        // 收起状态 - 只显示当前歌词
                        <div className="flex items-center gap-2">
                          {/* 模式切换按钮 - 固定在左侧 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleMusicMode();
                            }}
                            className="w-5 h-5 rounded-full bg-gradient-to-r from-blue-500/60 to-cyan-500/60 hover:from-blue-500/80 hover:to-cyan-500/80 flex items-center justify-center transition-all hover:scale-110 flex-shrink-0"
                            title="切换到故事模式"
                          >
                            <Music2 className="w-2.5 h-2.5 text-white" />
                          </button>
                          <div className="w-1 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full" />
                          <p className="text-white text-sm font-medium flex-1">
                            {currentLyric?.text || '♪ ♪ ♪'}
                          </p>
                          <ChevronDown className="w-3 h-3 text-white/60 flex-shrink-0" />
                        </div>
                      ) : (
                        // 展开状态 - 显示前后歌词
                        <div>
                          {/* 增强的标题栏 */}
                          <div className="flex items-center justify-between mb-3 pb-2 border-b border-white/10">
                            <div className="flex items-center gap-2">
                              {/* 放大的模式图标 */}
                              <div className="w-6 h-6 rounded-lg bg-gradient-to-r from-blue-500/40 to-cyan-500/40 flex items-center justify-center">
                                <Music2 className="w-4 h-4 text-white" />
                              </div>
                              {/* 更明显的标题 */}
                              <span className="text-white text-base font-semibold">音乐模式</span>
                              {/* 歌词进度指示器 */}
                              <div className="flex items-center gap-1 ml-2">
                                {lyrics.map((_, index) => (
                                  <div
                                    key={index}
                                    className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
                                      index === currentLyricIndex
                                        ? 'bg-blue-400 scale-125'
                                        : index < currentLyricIndex
                                        ? 'bg-blue-400/60'
                                        : 'bg-white/20'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                            
                            {/* 右侧按钮组 */}
                            <div className="flex items-center gap-2">
                              {/* 设为默认按钮 */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDefaultMusicMode('foreground');
                                  // 显示反馈
                                  setAiState({
                                    currentMessage: '已设置默认播放音乐模式 🎵',
                                    displayMessage: '',
                                    mood: 'happy',
                                    suggestions: [],
                                    isTyping: false
                                  });
                                }}
                                className={`flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium transition-all ${
                                  defaultMusicMode === 'foreground'
                                    ? 'bg-blue-500/30 text-blue-300 border border-blue-400/50'
                                    : 'bg-white/10 text-white/60 hover:bg-white/15 hover:text-white/80'
                                }`}
                              >
                                <Check className="w-3 h-3" />
                                <span>默认</span>
                              </button>
                              
                              {/* 模式切换按钮 */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleMusicMode();
                                }}
                                className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-500/60 to-pink-500/60 hover:from-purple-500/80 hover:to-pink-500/80 flex items-center justify-center transition-all hover:scale-110"
                                title="切换到故事模式"
                              >
                                <BookOpen className="w-3 h-3 text-white" />
                              </button>
                              
                              <ChevronUp className="w-3 h-3 text-white/60 flex-shrink-0" />
                            </div>
                          </div>
                          
                          {/* 歌词内容 */}
                          <div className="space-y-2">
                            {/* 前一句 */}
                            {currentLyricIndex > 0 && (
                              <p className="text-white/40 text-sm">
                                {lyrics[currentLyricIndex - 1].text}
                              </p>
                            )}
                            
                            {/* 当前句 - 高亮 */}
                            <p className="text-white text-lg font-medium">
                              {currentLyric?.text || '♪ ♪ ♪'}
                            </p>
                            
                            {/* 后一句 */}
                            {currentLyricIndex < lyrics.length - 1 && (
                              <p className="text-white/40 text-sm">
                                {lyrics[currentLyricIndex + 1].text}
                              </p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}


          {/* 底部区域 - 歌曲信息和播放控制 */}
          <div className="absolute bottom-0 left-0 right-0 z-30">
            {/* 歌曲信息区域 - 重新设计 */}
            <div className="px-6 pb-2">
              <div className="flex items-center justify-between mb-3">
                {/* 左侧：只显示作品标题 */}
                <div className="flex-1 min-w-0">
                  <h2 className="text-white text-lg font-semibold truncate">{currentSong.title}</h2>
                </div>

                {/* 右侧：保留歌单、wandSparkles、更多按钮 */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setShowPlaylist(true)}
                    className="w-8 h-8 rounded-full bg-black/30 backdrop-blur-md border border-white/20 flex items-center justify-center transition-all hover:scale-110"
                    title="歌单"
                  >
                    <List className="w-4 h-4 text-white/80" />
                  </button>

                  <button
                    onClick={() => setShowRemixPanel(true)}
                    className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500/60 to-pink-500/60 backdrop-blur-md flex items-center justify-center hover:scale-110 transition-all"
                    title="AI创作"
                  >
                    <Wand2 className="w-4 h-4 text-white" />
                  </button>

                  <button
                    onClick={() => setShowMoreOptions(true)}
                    className="w-8 h-8 rounded-full bg-black/30 backdrop-blur-md border border-white/20 flex items-center justify-center hover:scale-110 transition-all"
                    title="更多"
                  >
                    <MoreVertical className="w-4 h-4 text-white/80" />
                  </button>
                </div>
              </div>

              {/* 进度条 */}
              <div className="relative mb-4">
                <div className="h-0.5 bg-white/20 rounded-full">
                  <div
                    className="h-full bg-white rounded-full transition-all"
                    style={{ width: `${(currentTime / currentSong.duration) * 100}%` }}
                  />
                </div>
              </div>
            </div>

            {/* 新的底部导航栏 - 作者信息和互动按钮 */}
              <div className="px-6 pb-5 pt-3 bg-gradient-to-t from-black via-black/95 to-transparent">
                <div className="flex items-center justify-between">
                  {/* 左侧：作者信息 + 关注按钮 */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-sm font-medium">月</span>
                    </div>
                    
                    <div className="flex flex-col">
                      <p className="text-white text-sm font-medium">月光诗人</p>
                      {/* 关注按钮移到这里 */}
                      <button
                        onClick={handleFollow}
                        className={`mt-1 px-3 py-1 rounded-full transition-all duration-300 border text-xs font-medium ${
                          isFollowing
                            ? 'bg-gradient-to-r from-pink-500/20 to-purple-500/20 border-pink-400/50 text-pink-300'
                            : 'bg-gradient-to-r from-purple-500/20 to-blue-500/20 border-purple-400/50 text-purple-300 hover:from-purple-500/30 hover:to-blue-500/30'
                        } ${followAnimation ? 'animate-follow-bounce' : ''}`}
                      >
                        {isFollowing ? '已关注' : '关注'}
                      </button>
                    </div>
                  </div>

                  {/* 中间：互动按钮组 - 垂直布局 */}
                  <div className="flex items-center gap-5">
                    {/* 喜欢按钮 - 垂直布局 */}
                    <button
                      onClick={handleResonance}
                      className={`flex flex-col items-center gap-1 transition-all hover:scale-110 ${
                        resonanceAnimation ? 'animate-resonance-burst' : ''
                      }`}
                    >
                      <div className="w-10 h-10 rounded-full bg-black/30 backdrop-blur-md border border-white/20 flex items-center justify-center">
                        <Heart className={`w-5 h-5 ${isLiked ? 'text-pink-400 fill-current' : 'text-white/80'}`} />
                      </div>
                      <span className="text-white/80 text-xs font-medium">
                        {formatNumber(currentSong.stats.likes)}
                      </span>
                    </button>

                    {/* 评论按钮 - 垂直布局 */}
                    <button
                      onClick={() => setShowAllComments(true)}
                      className="flex flex-col items-center gap-1 hover:scale-110 transition-all"
                    >
                      <div className="w-10 h-10 rounded-full bg-black/30 backdrop-blur-md border border-white/20 flex items-center justify-center">
                        <MessageCircle className="w-5 h-5 text-white/80" />
                      </div>
                      <span className="text-white/80 text-xs font-medium">评论</span>
                    </button>

                    {/* 分享按钮 - 垂直布局 */}
                    <button
                      onClick={() => {
                        console.log('分享歌曲:', currentSong.title);
                        // 这里可以添加分享逻辑，比如显示分享弹窗
                        setAiState({
                          currentMessage: '分享链接已复制到剪贴板 📋',
                          displayMessage: '',
                          mood: 'happy',
                          suggestions: ['分享到微信', '生成分享海报', '发送给朋友'],
                          isTyping: false
                        });
                      }}
                      className="flex flex-col items-center gap-1 hover:scale-110 transition-all"
                    >
                      <div className="w-10 h-10 rounded-full bg-black/30 backdrop-blur-md border border-white/20 flex items-center justify-center">
                        <Share2 className="w-5 h-5 text-white/80" />
                      </div>
                      <span className="text-white/80 text-xs font-medium">分享</span>
                    </button>
                  </div>

                  {/* 右侧：AI音律球 */}
                  <button
                    onClick={(e) => {
                      // 阻止事件冒泡
                      e.stopPropagation();

                      // 使用延迟判断区分单击/双击，避免双击时触发单击逻辑
                      if (aiOrbClickTimerRef.current) {
                        clearTimeout(aiOrbClickTimerRef.current);
                        aiOrbClickTimerRef.current = null;
                      }
                      aiOrbClickTimerRef.current = setTimeout(() => {
                        openAiChat();
                        aiOrbClickTimerRef.current = null;

                        // 添加视觉反馈（单击）
                        setAiOrbAnimation(true);
                        setTimeout(() => setAiOrbAnimation(false), 300);
                      }, 250);
                    }}
                    onDoubleClick={(e) => {
                      // 阻止事件冒泡
                      e.stopPropagation();

                      // 双击发生时，清除单击定时器，防止打开聊天蒙层
                      if (aiOrbClickTimerRef.current) {
                        clearTimeout(aiOrbClickTimerRef.current);
                        aiOrbClickTimerRef.current = null;
                      }

                      // 双击逻辑：切换顶部AI小丝带显示
                      toggleAiRibbon();

                      // 添加双击视觉反馈
                      setAiOrbAnimation(true);
                      setTimeout(() => setAiOrbAnimation(false), 500);
                    }}
                    className={`w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg ${
                      aiOrbAnimation ? 'animate-pulse scale-110' : ''
                    }`}
                    title="AI音律球 - 单击打开聊天，双击切换丝带"
                  >
                    <Sparkles className="w-6 h-6 text-white" />
                  </button>
                </div>
              </div>
          </div>

          {/* 播放列表模态框 */}
          {showPlaylist && (
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-40 flex items-end animate-fade-in rounded-[2.8rem] overflow-hidden"
              onClick={() => setShowPlaylist(false)}
            >
              <div className="w-full bg-black/95 backdrop-blur-xl rounded-t-3xl animate-slide-up" style={{ maxHeight: '75%' }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* 拖动指示条 */}
                <div className="flex justify-center pt-3 pb-2">
                  <div className="w-12 h-1.5 bg-white/20 rounded-full" />
                </div>
                
                {/* Tab 切换栏 */}
                <div className="px-6 pb-3">
                  <div className="flex bg-white/5 rounded-2xl p-1">
                    <button
                      onClick={() => setPlaylistMode('normal')}
                      className={`flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl transition-all ${
                        playlistMode === 'normal' 
                          ? 'bg-white/15 text-white' 
                          : 'text-white/50 hover:text-white/70'
                      }`}
                    >
                      <Music2 className="w-4 h-4" />
                      <span className="text-sm font-medium">播放列表</span>
                    </button>
                    <button
                      onClick={() => setPlaylistMode('story')}
                      className={`flex-1 flex items-center justify-center gap-2 py-2.5 rounded-xl transition-all relative ${
                        playlistMode === 'story' 
                          ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-purple-500/30' 
                          : 'text-white/50 hover:text-white/70'
                      }`}
                    >
                      <BookOpen className="w-4 h-4" />
                      <span className="text-sm font-medium">故事系列</span>
                      {currentStoryId && playlistMode === 'normal' && (
                        <span className="absolute -top-1 -right-1 w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                      )}
                    </button>
                  </div>
                </div>
                
                {/* 普通播放列表模式 */}
                {playlistMode === 'normal' && (
                  <>
                    {/* 标题栏 */}
                    <div className="px-6 pb-4 border-b border-white/10">
                      <div className="flex items-center justify-between">
                        <h3 className="text-white font-semibold text-lg">当前播放列表</h3>
                        <div className="flex items-center gap-2">
                          {/* 播放模式按钮 */}
                          <button
                            onClick={() => {
                              const modes: ('sequence' | 'shuffle' | 'repeat')[] = ['sequence', 'shuffle', 'repeat'];
                              const currentIndex = modes.indexOf(playMode);
                              setPlayMode(modes[(currentIndex + 1) % modes.length]);
                            }}
                            className="p-2.5 bg-white/10 hover:bg-white/15 rounded-full transition-all"
                          >
                            {playMode === 'sequence' && <SkipForward className="w-5 h-5 text-white" />}
                            {playMode === 'shuffle' && <Shuffle className="w-5 h-5 text-white" />}
                            {playMode === 'repeat' && <Repeat className="w-5 h-5 text-white" />}
                          </button>
                          
                          {/* AI续播开关 */}
                          <button
                            onClick={() => setAiContinuePlay(!aiContinuePlay)}
                            className={`flex items-center gap-2 px-3 py-2 rounded-full transition-all ${
                              aiContinuePlay 
                                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' 
                                : 'bg-white/10 text-white/60'
                            }`}
                          >
                            <Sparkles className="w-4 h-4" />
                            <span className="text-xs font-medium">智能续播</span>
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    {/* 歌曲列表 */}
                    <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(75vh - 180px)' }}>
                      <div className="space-y-2">
                        {playlist.map((song, index) => (
                          <div
                            key={song.id}
                            className={`flex items-center gap-4 p-3 rounded-xl transition-all ${
                              index === currentSongIndex
                                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30'
                                : 'bg-white/5 hover:bg-white/10 border border-white/10'
                            }`}
                          >
                            {/* 序号/播放状态 */}
                            <div 
                              className="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center cursor-pointer"
                              onClick={() => {
                                setCurrentSongIndex(index);
                                setShowPlaylist(false);
                              }}
                            >
                              {index === currentSongIndex && isPlaying ? (
                                <div className="flex gap-0.5">
                                  <div className="w-0.5 h-3 bg-white rounded-full animate-wave" style={{ animationDelay: '0ms' }} />
                                  <div className="w-0.5 h-3 bg-white rounded-full animate-wave" style={{ animationDelay: '150ms' }} />
                                  <div className="w-0.5 h-3 bg-white rounded-full animate-wave" style={{ animationDelay: '300ms' }} />
                                </div>
                              ) : (
                                <span className="text-white/60 text-sm font-medium">{index + 1}</span>
                              )}
                            </div>
                            
                            {/* 歌曲信息 */}
                            <div 
                              className="flex-1 cursor-pointer"
                              onClick={() => {
                                setCurrentSongIndex(index);
                                setShowPlaylist(false);
                              }}
                            >
                              <h4 className={`font-medium text-sm ${
                                index === currentSongIndex ? 'text-white' : 'text-white/90'
                              }`}>
                                {song.title}
                              </h4>
                              <p className="text-xs text-white/50">{song.artist} · {formatTime(song.duration)}</p>
                            </div>
                            
                            {/* 心情标签 */}
                            <span className="text-lg">{song.moodEmoji}</span>
                            
                            {/* 删除按钮 */}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                console.log('删除歌曲:', song.title);
                              }}
                              className="p-2 hover:bg-white/10 rounded-full transition-all group"
                            >
                              <X className="w-4 h-4 text-white/40 group-hover:text-red-400 transition-colors" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
                
                {/* 音乐故事模式 */}
                {playlistMode === 'story' && (
                  <>
                    {/* 故事信息头部 */}
                    <div className="px-6 pb-4 border-b border-white/10">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <BookOpen className="w-4 h-4 text-purple-400" />
                            <span className="text-xs text-purple-400 font-medium">音乐故事</span>
                          </div>
                          <h3 className="text-white font-semibold text-lg mb-1">{storyData.title}</h3>
                          <p className="text-white/60 text-xs leading-relaxed">{storyData.description}</p>
                        </div>
                        <button
                          onClick={() => {
                            console.log('探索更多故事');
                            setShowPlaylist(false);
                          }}
                          className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white text-xs font-medium hover:scale-105 transition-transform whitespace-nowrap"
                        >
                          探索更多故事
                        </button>
                      </div>
                      
                      {/* 故事进度条 */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-white/50">故事进度</span>
                          <span className="text-white/70">第 {currentStoryChapter} / {storyData.totalChapters} 章</span>
                        </div>
                        <div className="h-1.5 bg-white/10 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all"
                            style={{ width: `${(currentStoryChapter / storyData.totalChapters) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                    
                    {/* 故事章节列表 */}
                    <div className="px-6 py-4 overflow-y-auto" style={{ maxHeight: 'calc(75vh - 240px)' }}>
                      <div className="space-y-2">
                        {storyPlaylist.map((chapter, index) => (
                          <div
                            key={chapter.id}
                            className={`relative overflow-hidden ${
                              index === currentStoryChapter - 1
                                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30'
                                : chapter.played
                                ? 'bg-white/5 border border-white/10'
                                : 'bg-white/5 hover:bg-white/10 border border-white/10'
                            } rounded-xl transition-all cursor-pointer`}
                            onClick={() => {
                              setCurrentStoryChapter(index + 1);
                              setShowPlaylist(false);
                            }}
                          >
                            {/* 章节背景装饰 */}
                            {index === currentStoryChapter - 1 && (
                              <div className="absolute inset-0 opacity-10">
                                <div className="absolute -top-20 -right-20 w-40 h-40 bg-purple-500 rounded-full blur-3xl" />
                                <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-pink-500 rounded-full blur-3xl" />
                              </div>
                            )}
                            
                            <div className="relative flex items-center gap-4 p-4">
                              {/* 章节编号 */}
                              <div className="flex flex-col items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20">
                                <span className="text-[10px] text-white/50">第</span>
                                <span className="text-sm font-bold text-white">{index + 1}</span>
                                <span className="text-[10px] text-white/50">章</span>
                              </div>
                              
                              {/* 章节信息 */}
                              <div className="flex-1">
                                <h4 className={`font-medium text-sm mb-1 ${
                                  index === currentStoryChapter - 1 ? 'text-white' : 'text-white/90'
                                }`}>
                                  {chapter.title}
                                </h4>
                                <p className="text-xs text-white/50 mb-1">{chapter.subtitle}</p>
                                <div className="flex items-center gap-3 text-xs text-white/40">
                                  <span>{formatTime(chapter.duration)}</span>
                                  {chapter.played && (
                                    <span className="flex items-center gap-1 text-purple-400">
                                      <Check className="w-3 h-3" />
                                      已收听
                                    </span>
                                  )}
                                </div>
                              </div>
                              
                              {/* 播放状态 */}
                              {index === currentStoryChapter - 1 && isPlaying && (
                                <div className="flex gap-0.5">
                                  <div className="w-0.5 h-4 bg-purple-400 rounded-full animate-wave" style={{ animationDelay: '0ms' }} />
                                  <div className="w-0.5 h-4 bg-purple-400 rounded-full animate-wave" style={{ animationDelay: '150ms' }} />
                                  <div className="w-0.5 h-4 bg-purple-400 rounded-full animate-wave" style={{ animationDelay: '300ms' }} />
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      {/* 故事结尾提示 */}
                      <div className="mt-6 p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20 text-center">
                        <p className="text-white/70 text-sm mb-2">更多精彩章节正在众筹</p>
                        <button className="text-purple-400 text-xs hover:text-purple-300 transition-colors">
                          去支持作者 →
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Home Indicator */}
          <div className="absolute bottom-1.5 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full z-50" />
          
          {/* AIChat 组件 - 统一状态管理 */}
          <AIChat
            isOpen={aiUIState.showChat}
            onClose={closeAiChat}
            currentPage="home"
            contextData={{
              isPlaying,
              currentSong,
              playProgress: (currentTime / currentSong.duration) * 100
            }}
            ribbonMode={aiUIState.showRibbon && aiVisible && !isImmersiveMode}
            ribbonVisible={aiUIState.ribbonVisible}
            allowOverlay={true}
            onRibbonToggle={(visible) => {
              setAiUIState(prev => ({ ...prev, ribbonVisible: visible }));
            }}
            onGuiAction={(action) => {
              if (action === 'open_full_chat') {
                openAiChat();
              }
            }}
          />
          
          {/* 评论全屏面板 */}
          {showAllComments && (
            <div className="absolute inset-0 bg-black/95 backdrop-blur-xl z-50 animate-slide-up rounded-[2.8rem]">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between p-6">
                  <h2 className="text-white text-xl font-semibold">音乐共鸣</h2>
                  <button onClick={() => setShowAllComments(false)}>
                    <X className="w-6 h-6 text-white/80" />
                  </button>
                </div>
                <div className="flex-1 overflow-y-auto px-6 pb-20">
                  <div className="space-y-4">
                    {mockDanmakus.map(comment => (
                      <div key={comment.id} className="flex gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex-shrink-0" />
                        <div>
                          <p className="text-white/80 font-medium text-sm mb-1">{comment.userName}</p>
                          <p className="text-white/90">{comment.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="p-4 border-t border-white/10">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="写下你的感受..."
                      className="flex-1 bg-white/10 rounded-full px-4 py-3 text-white placeholder-white/40 outline-none"
                    />
                    <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-medium">
                      发送
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 二创面板 - 从下往上的模态框 */}
          {showRemixPanel && (
            <>
              {/* 背景遮罩 - 点击关闭 */}
              <div 
                className="absolute inset-0 bg-black/50 backdrop-blur-sm z-40"
                onClick={() => setShowRemixPanel(false)}
              />
              
              {/* 模态框内容 - 限制在手机尺寸内 */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/95 backdrop-blur-xl z-50 rounded-t-3xl animate-modal-slide-up">
                <div className="flex flex-col max-h-[80vh]">
                  {/* 拖拽指示器 */}
                  <div className="flex justify-center pt-3 pb-2">
                    <div className="w-10 h-1 bg-white/30 rounded-full" />
                  </div>
                  
                  {/* Tab切换 */}
                  <div className="px-6 pb-3">
                    <div className="flex bg-white/5 rounded-2xl p-1">
                      <button
                        onClick={() => setRemixModalTab('quick')}
                        className={`flex-1 py-2.5 rounded-xl transition-all text-sm font-medium ${
                          remixModalTab === 'quick' 
                            ? 'bg-white/15 text-white' 
                            : 'text-white/50 hover:text-white/70'
                        }`}
                      >
                        快速微调
                      </button>
                      <button
                        onClick={() => setRemixModalTab('creative')}
                        className={`flex-1 py-2.5 rounded-xl transition-all text-sm font-medium relative ${
                          remixModalTab === 'creative' 
                            ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border border-purple-500/30' 
                            : 'text-white/50 hover:text-white/70'
                        }`}
                      >
                        创意演绎
                        {remixModalTab === 'quick' && (
                          <span className="absolute -top-1 -right-1 w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  {/* Tab内容区域 */}
                  {remixModalTab === 'quick' ? (
                    <>
                      {/* 快速微调内容 */}
                      <div className="flex items-center justify-between px-6 py-4">
                        <h2 className="text-white text-xl font-semibold">快速微调</h2>
                      </div>
                      
                      <div className="flex-1 overflow-y-auto px-6">
                        <div className="grid grid-cols-2 gap-3">
                          {currentSong.remixVersions.map(remix => (
                            <button
                              key={remix.id}
                              onClick={() => handleQuickRemix(remix)}
                              className="bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all"
                            >
                              <span className="text-3xl mb-2 block">{remix.emoji}</span>
                              <p className="text-white font-medium text-sm mb-1">{remix.label}</p>
                              <p className="text-white/60 text-xs">{remix.description}</p>
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      {/* 底部按钮 */}
                      <div className="px-6 py-4 border-t border-white/10">
                        <button className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl text-white font-medium text-sm">
                          去深度二创
                        </button>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* 创意演绎内容 */}
                      <div className="flex items-center justify-between px-6 py-4">
                        <div>
                          <h2 className="text-white text-xl font-semibold">创意演绎</h2>
                          <p className="text-white/60 text-xs mt-1">网友优秀二创作品</p>
                        </div>
                        <span className="text-sm text-white/50">{remixSongsList.length} 个版本</span>
                      </div>
                      
                      <div className="flex-1 overflow-y-auto px-6 pb-4">
                        <div className="space-y-3">
                          {remixSongsList.map((remix) => {
                            const IconComponent = getRemixTypeIcon(remix.remixType);
                            return (
                              <div
                                key={remix.id}
                                className="group relative overflow-hidden bg-white/5 hover:bg-white/10 rounded-xl p-4 border border-white/10 hover:border-cyan-400/30 transition-all cursor-pointer"
                                onClick={() => {
                                  console.log('播放二创:', remix.title);
                                  setShowRemixPanel(false);
                                }}
                              >
                                {/* 背景装饰 */}
                                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-full blur-xl group-hover:scale-110 transition-transform" />
                                
                                <div className="relative flex items-center gap-4">
                                  {/* 左侧图标和类型 */}
                                  <div className="flex flex-col items-center gap-1">
                                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${getIntensityColor(remix.remixIntensity)} flex items-center justify-center shadow-lg`}>
                                      <IconComponent className="w-6 h-6 text-white" />
                                    </div>
                                    <span className="text-xs text-white/50 text-center">{getRemixTypeLabel(remix.remixType)}</span>
                                  </div>
                                  
                                  {/* 中间信息 */}
                                  <div className="flex-1 min-w-0">
                                    <h4 className="text-white font-medium text-sm group-hover:text-cyan-300 transition-colors truncate mb-1">
                                      {remix.title}
                                    </h4>
                                    
                                    <p className="text-xs text-white/60 mb-2">{remix.artist}</p>
                                    
                                    {/* 亮点描述 */}
                                    <div className="flex items-center gap-2 mb-2">
                                      <Flame className="w-3 h-3 text-orange-400 flex-shrink-0" />
                                      <span className="text-xs text-orange-300 font-medium truncate">{remix.highlight}</span>
                                    </div>
                                    
                                    {/* 数据统计 */}
                                    <div className="flex items-center gap-4 text-xs text-white/40">
                                      <span className="flex items-center gap-1">
                                        <Eye className="w-3 h-3" />
                                        {formatNumber(remix.plays)}
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <Heart className="w-3 h-3" />
                                        {formatNumber(remix.likes)}
                                      </span>
                                    </div>
                                  </div>
                                  
                                  {/* 右侧时长 */}
                                  <div className="text-xs text-white/40 font-mono">
                                    {formatTime(remix.duration)}
                                  </div>
                                </div>
                                
                                {/* 悬浮效果 */}
                                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl" />
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      
                      {/* 底部按钮 */}
                      <div className="px-6 py-4 border-t border-white/10">
                        <button 
                          className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl text-white font-medium text-sm hover:scale-105 transition-transform"
                          onClick={() => {
                            console.log('查看更多二创歌曲');
                            // 这里可以导航到二创歌曲列表页面
                          }}
                        >
                          查看更多二创歌曲 →
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </>
          )}

          {/* 更多选项面板 - 从下往上的模态框 */}
          {showMoreOptions && (
            <>
              {/* 背景遮罩 - 点击关闭 */}
              <div 
                className="absolute inset-0 bg-black/50 backdrop-blur-sm z-40"
                onClick={() => setShowMoreOptions(false)}
              />
              
              {/* 模态框内容 - 限制在手机尺寸内 */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/95 backdrop-blur-xl z-50 rounded-t-3xl animate-modal-slide-up">
                <div className="flex flex-col">
                  {/* 拖拽指示器 */}
                  <div className="flex justify-center pt-3 pb-2">
                    <div className="w-10 h-1 bg-white/30 rounded-full" />
                  </div>
                  
                  <div className="flex items-center justify-between px-6 py-4">
                    <h2 className="text-white text-xl font-semibold">更多选项</h2>
                  </div>
                  
                  <div className="px-6 pb-6">
                    <div className="space-y-3">
                      {/* 灵感回响 */}
                      <button className="w-full bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center">
                          <Sparkles className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="text-white font-medium">灵感回响</p>
                          <p className="text-white/60 text-sm">查看创作时的AI聊天记录</p>
                        </div>
                        <ChevronUp className="w-5 h-5 text-white/60 rotate-90" />
                      </button>
                      
                      {/* 分享 */}
                      <button className="w-full bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-cyan-400 flex items-center justify-center">
                          <Share2 className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="text-white font-medium">分享</p>
                          <p className="text-white/60 text-sm">分享给朋友</p>
                        </div>
                        <ChevronUp className="w-5 h-5 text-white/60 rotate-90" />
                      </button>
                      
                      {/* 下载 */}
                      <button className="w-full bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-400 to-emerald-400 flex items-center justify-center">
                          <ArrowDown className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="text-white font-medium">下载</p>
                          <p className="text-white/60 text-sm">保存到本地</p>
                        </div>
                        <ChevronUp className="w-5 h-5 text-white/60 rotate-90" />
                      </button>

                      {/* 查看封面 */}
                      <button className="w-full bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-amber-400 to-orange-400 flex items-center justify-center">
                          <Eye className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="text-white font-medium">查看封面</p>
                          <p className="text-white/60 text-sm">双击空白区域也可查看</p>
                        </div>
                        <ChevronUp className="w-5 h-5 text-white/60 rotate-90" />
                      </button>
                      
                      {/* 举报 */}
                      <button className="w-full bg-white/10 hover:bg-white/20 rounded-2xl p-4 transition-all flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-red-400 to-orange-400 flex items-center justify-center">
                          <Flag className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="text-white font-medium">举报</p>
                          <p className="text-white/60 text-sm">举报内容问题</p>
                        </div>
                        <ChevronUp className="w-5 h-5 text-white/60 rotate-90" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
 }

  .animate-blink {
    animation: blink 1s infinite;
  }
  @keyframes float-particle {
    0% {
      transform: translateY(100vh) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 0.2;
    }
    90% {
      opacity: 0.2;
    }
    100% {
      transform: translateY(-100vh) rotate(360deg);
      opacity: 0;
    }
  }
  
  @keyframes slide-down {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes slide-up {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes modal-slide-up {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
  }
  
  @keyframes slide-left {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes resonance-burst {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
  }
  
  @keyframes follow-bounce {
    0% { transform: scale(1); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1.05); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
  
  .animate-danmaku {
    animation: danmaku linear infinite;
  }
  
  .animate-float-particle {
    animation: float-particle linear infinite;
  }
  
  .animate-slide-down {
    animation: slide-down 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }
  
  .animate-modal-slide-up {
    animation: modal-slide-up 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .animate-slide-left {
    animation: slide-left 0.3s ease-out;
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-resonance-burst {
    animation: resonance-burst 0.5s ease-out;
  }
  
  .animate-follow-bounce {
    animation: follow-bounce 0.8s ease-out;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
`;
document.head.appendChild(style);

export default MusicAppreciationPage;
