import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  <PERSON>, Sparkles, Play, Heart, MessageCircle, Share2, X, Send,
  ChevronDown, ChevronUp, Layers, Home, Search, TrendingUp,
  Headphones, Gift, Moon, Sun, Coffee, BookOpen, Users, Trophy,
  Clock, Volume2, SkipForward, Pause, Settings, Target, Zap,
  Minimize2, Maximize2, <PERSON><PERSON>ointer, Star, Flame, PenTool,
  Hash, Calendar, ArrowRight, Loader2, Eye, EyeOff, Brain,
  ChevronRight, Image
} from 'lucide-react';
import xiaoxianImage from '../assets/xiaoxian.png';

// 类型定义区域
interface MoodColor {
  primary: string;
  secondary?: string;
  particles?: string[];
  glow?: string;
  background?: string;
  accent?: string;
}

interface Task {
  id: string;
  type: 'notification' | 'generation' | 'system';
  title: string;
  subtitle?: string;
  icon: React.ComponentType<any>;
  count?: number;
  timestamp: Date;
  status: 'pending' | 'completed' | 'error';
  action?: () => void;
  color: string; // 渐变色配置
}

interface TaskCategory {
  type: 'notification' | 'generation';
  icon: React.ComponentType<any>;
  count: number;
  color: string;
  title: string;
}

const moodColors: Record<string, MoodColor> = {
  happy: {
    primary: 'from-yellow-400 via-amber-400 to-orange-400'
  },
  calm: {
    primary: 'from-blue-400 via-cyan-400 to-teal-400'
  },
  excited: {
    primary: 'from-purple-400 via-pink-400 to-rose-400'
  },
  curious: {
    primary: 'from-indigo-400 via-purple-400 to-violet-400'
  }
};

interface AiQuickReply { text: string; action: string; }
interface AiTalk { message: string; quickReplies?: AiQuickReply[]; mood?: 'happy' | 'calm' | 'excited' | 'curious'; }

// ==================== 类型定义 ====================
export interface AIMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  card?: InteractiveCard;
  suggestions?: string[];
  isTyping?: boolean;
}

interface InteractiveCard {
  type: 'music_recommendation' | 'creation_params' | 'lyrics_card' | 'task_confirm' | 'story_card' | 'creation_activity' | 'daily_highlight';  // 新增两个类型
  data: any;
  actions?: CardAction[];
}

interface CardAction {
  label: string;
  action: string;
  primary?: boolean;
  destructive?: boolean;
}

interface ContextAnchor {
  type: 'playing' | 'creating' | 'browsing' | 'viewing';
  title: string;
  subtitle?: string;
  progress?: number;
  icon?: React.ComponentType<any>;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  action: () => void;
  highlight?: boolean;
}

interface MagicPointerTarget {
  element: HTMLElement;
  label: string;
  type: string;
}

export interface AIChatProps {
  isOpen: boolean;
  onClose: () => void;
  currentPage?: string;
  contextData?: any;
  onGuiAction?: (action: string, data?: any) => void;
  // 新增：丝带模式相关属性
  ribbonMode?: boolean; // 是否以丝带模式显示（替代窥探模式）
  ribbonVisible?: boolean; // 丝带内容是否可见
  allowOverlay?: boolean; // 是否允许丝带遮挡页面内容
  onRibbonToggle?: (visible: boolean) => void; // 丝带显示/隐藏回调
}

// ==================== 主组件 ====================
const AIChat: React.FC<AIChatProps> = ({
  isOpen,
  onClose,
  currentPage = 'home',
  contextData = {},
  onGuiAction,
  ribbonMode = false,
  ribbonVisible = true,
  allowOverlay = false,
  onRibbonToggle
}) => {
  // ========== 状态管理 ==========
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isPeekMode, setIsPeekMode] = useState(false);
  const [isMagicPointerActive, setIsMagicPointerActive] = useState(false);

  const [currentAnchor, setCurrentAnchor] = useState<ContextAnchor | null>(null);
  const [selectedElement, setSelectedElement] = useState<MagicPointerTarget | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [showTaskPanel, setShowTaskPanel] = useState(false);

  // AI ribbon states (统一的丝带状态管理)
  // 使用外部传入的ribbonVisible状态，不再维护内部状态
  const showRibbon = ribbonVisible;
  const [currentMood, setCurrentMood] = useState<'happy' | 'calm' | 'excited' | 'curious'>('excited');
  const [currentAiTalk, setCurrentAiTalk] = useState<AiTalk>({ message: '' });
  const [aiTalkMessage, setAiTalkMessage] = useState('');
  
  // Refs
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);
  const welcomeTimersRef = useRef<NodeJS.Timeout[]>([]);
  const hasAddedWelcomeMessages = useRef<boolean>(false);

  // ========== 初始化 ==========
  useEffect(() => {
    if ((isOpen || ribbonMode) && messages.length === 0) {
      // 重置欢迎消息标志
      hasAddedWelcomeMessages.current = false;

      // 根据当前页面生成初始欢迎消息
      const welcomeMessage = generateWelcomeMessage();
      setMessages([welcomeMessage]);

      // 设置上下文锚点
      updateContextAnchor();
    }
  }, [isOpen, ribbonMode, currentPage]);

  // 清理定时器
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有定时器
      welcomeTimersRef.current.forEach(timer => clearTimeout(timer));
      welcomeTimersRef.current = [];
      if (typingTimer.current) clearTimeout(typingTimer.current);
      if (longPressTimer.current) clearTimeout(longPressTimer.current);
    };
  }, []);

  // 丝带模式初始化
  useEffect(() => {
    if (ribbonMode) {
      updateAiTalk();
    }
  }, [ribbonMode]);

  // 滚动到底部
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [messages]);

  // ========== 上下文感知 ==========
  const updateContextAnchor = useCallback(() => {
    // 根据contextData更新锚点
    if (contextData.isPlaying) {
      setCurrentAnchor({
        type: 'playing',
        title: contextData.currentSong?.title || '未知歌曲',
        subtitle: contextData.currentSong?.artist,
        progress: contextData.playProgress || 0,
        icon: Music
      });
    } else if (contextData.isCreating) {
      setCurrentAnchor({
        type: 'creating',
        title: contextData.creationTitle || '新作品',
        subtitle: '创作中',
        progress: contextData.creationProgress || 0,
        icon: PenTool
      });
    } else {
      setCurrentAnchor({
        type: 'browsing',
        title: getPageTitle(currentPage),
        icon: Home
      });
    }
  }, [contextData, currentPage]);

  const getPageTitle = (page: string): string => {
    const titles: Record<string, string> = {
      home: '首页浏览',
      create: '创作页面',
      profile: '个人主页',
      discover: '发现音乐'
    };
    return titles[page] || '心弦 MoodBeat';
  };

  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      type: 'notification',
      title: '小明评论了你的作品',
      subtitle: '"这首歌太棒了！"',
      icon: MessageCircle,
      timestamp: new Date(Date.now() - 1000 * 60 * 5),
      status: 'pending',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: '2',
      type: 'notification',
      title: '你的作品获得了100个喜欢',
      subtitle: '《夏日微风》',
      icon: Heart,
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      status: 'pending',
      color: 'from-pink-500 to-rose-500'
    },
    {
      id: '3',
      type: 'generation',
      title: '正在生成：梦幻电音',
      subtitle: '预计还需2分钟',
      icon: Loader2,
      timestamp: new Date(),
      status: 'pending',
      color: 'from-purple-500 to-violet-500'
    },
    {
      id: '4',
      type: 'generation',
      title: '生成完成：午夜爵士',
      subtitle: '点击试听',
      icon: Music,
      timestamp: new Date(Date.now() - 1000 * 60 * 2),
      status: 'completed',
      color: 'from-green-500 to-emerald-500'
    }
  ]);

  // 时间格式化函数
  const formatTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return date.toLocaleDateString();
  };

const generateWelcomeMessage = (): AIMessage => {
  const hour = new Date().getHours();
  let content = '';
  let card: InteractiveCard | undefined;

  // 仅在首页展示丰富内容
  if (currentPage === 'home') {
    if (hour >= 6 && hour < 12) {
      content = '早安！🌅 我是小弦，今天有超棒的音乐活动等你参加哦～快来看看今日精选！';
    } else if (hour >= 22 || hour < 6) {
      content = '夜深了，来点助眠音乐吗？不过今天的创作挑战还有最后几小时哦！💫';
    } else {
      content = '嗨！欢迎回来～今天的音乐社区超热闹，快来看看大家都在玩什么！🎵';
    }

    // 自动添加多条欢迎消息（模拟连续对话）- 防止重复添加
    if (!hasAddedWelcomeMessages.current) {
      hasAddedWelcomeMessages.current = true;

      // 清理之前的定时器
      welcomeTimersRef.current.forEach(timer => clearTimeout(timer));
      welcomeTimersRef.current = [];

      const timer1 = setTimeout(() => {
        // 今日精选卡片
        const highlightMessage: AIMessage = {
          id: 'highlight-' + Date.now(),
          type: 'ai',
          content: '这是今日为你精选的热门作品，每一首都是社区精华：',
          timestamp: new Date(),
          card: {
            type: 'daily_highlight',
            data: {
              title: '🔥 今日热门精选',
              date: new Date().toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
              songs: [
                {
                  id: 1,
                  title: '夏日晚风',
                  artist: '音乐创作者小明',
                  mood: 'happy',
                  plays: 12534,
                  likes: 892,
                  coverGradient: 'from-orange-400 to-pink-400',
                  trending: true
                },
                {
                  id: 2,
                  title: '都市夜归人',
                  artist: '深夜电台',
                  mood: 'calm',
                  plays: 8421,
                  likes: 623,
                  coverGradient: 'from-blue-400 to-purple-400'
                },
                {
                  id: 3,
                  title: '追梦少年',
                  artist: '青春记忆',
                  mood: 'excited',
                  plays: 18923,
                  likes: 1280,
                  coverGradient: 'from-green-400 to-cyan-400',
                  trending: true
                }
              ]
            },
            actions: [
              { label: '播放全部', action: 'play_all', primary: true },
              { label: '查看更多', action: 'view_more' }
            ]
          }
        };
        setMessages(prev => [...prev, highlightMessage]);

        // 创作活动卡片
        const timer2 = setTimeout(() => {
          const activityMessage: AIMessage = {
            id: 'activity-' + Date.now(),
            type: 'ai',
            content: '今天的创作挑战超有意思！已经有很多小伙伴参加了：',
            timestamp: new Date(),
            card: {
              type: 'creation_activity',
              data: {
                id: 'summer-memory-2024',
                title: '夏天的回忆',
                description: '用音乐记录这个夏天最美好的瞬间',
                endTime: new Date(Date.now() + 1000 * 60 * 60 * 18), // 18小时后结束
                participants: 1234,
                prize: {
                  first: { amount: 5000, unit: '元', badge: '🏆' },
                  second: { amount: 3000, unit: '元', badge: '🥈' },
                  third: { amount: 1000, unit: '元', badge: '🥉' },
                  participation: { amount: 100, unit: '积分', badge: '✨' }
                },
                tags: ['夏日', '青春', '回忆'],
                difficulty: '简单',
                hotLevel: 95, // 热度百分比
                topWorks: [
                  { title: '西瓜汽水', author: '夏日微风', likes: 523 },
                  { title: '海边日落', author: '追光者', likes: 412 }
                ]
              },
              actions: [
                { label: '立即参加', action: 'join_activity', primary: true },
                { label: '听听作品', action: 'listen_works' },
                { label: '查看规则', action: 'view_rules' }
              ]
            },
            suggestions: ['我要参加活动', '看看排行榜', '了解奖励']
          };
          setMessages(prev => [...prev, activityMessage]);
        }, 1500);

        welcomeTimersRef.current.push(timer2);
      }, 800);

      welcomeTimersRef.current.push(timer1);
    }

    return {
      id: 'welcome',
      type: 'ai',
      content,
      timestamp: new Date(),
      suggestions: ['今日推荐', '创作挑战', '热门排行']
    };
  } else {
    // 其他页面保持原有逻辑
    content = '嗨！我是小弦，你的音乐创作伙伴。有什么可以帮你的吗？';
    return {
      id: 'welcome',
      type: 'ai',
      content,
      timestamp: new Date(),
      suggestions: getContextualSuggestions()
    };
  }
};
  
  // 计算任务统计
  const taskStats = useMemo(() => {
  const notifications = tasks.filter(t => t.type === 'notification' && t.status === 'pending');
  const generations = tasks.filter(t => t.type === 'generation' && t.status === 'pending');
  return {
    notifications: notifications.length,
    generations: generations.length,
    total: notifications.length + generations.length
  };
}, [tasks]);

  // ========== 快捷操作生成 ==========
  const quickActions = useMemo((): QuickAction[] => {
    const baseActions: QuickAction[] = [];
    
    if (currentPage === 'home') {
      baseActions.push(
        { id: 'recommend', label: '推荐今日精选', icon: Sparkles, action: () => handleQuickAction('recommend'), highlight: true },
        { id: 'create', label: '创作新歌', icon: PenTool, action: () => handleQuickAction('create') },
        { id: 'hot', label: '热门挑战', icon: Flame, action: () => handleQuickAction('hot') },
        { id: 'story', label: '音乐故事', icon: BookOpen, action: () => handleQuickAction('story') }
      );
    } else if (currentPage === 'create') {
      baseActions.push(
        { id: 'style', label: '换个风格', icon: Layers, action: () => handleQuickAction('style') },
        { id: 'lyrics', label: '帮我写词', icon: PenTool, action: () => handleQuickAction('lyrics'), highlight: true },
        { id: 'preview', label: '试听效果', icon: Headphones, action: () => handleQuickAction('preview') }
      );
    }
    
    // 根据当前状态添加动态操作
    if (contextData.isPlaying) {
      baseActions.unshift({
        id: 'similar', 
        label: '相似推荐', 
        icon: Search, 
        action: () => handleQuickAction('similar')
      });
    }
    
    return baseActions;
  }, [currentPage, contextData]);

  const getContextualSuggestions = (): string[] => {
    if (currentPage === 'home') {
      return ['随便听听', '创作一首歌', '看看热门'];
    } else if (currentPage === 'create') {
      return ['优化歌词', '调整节奏', '添加和声'];
    }
    return ['有什么推荐', '帮我创作'];
  };

  // ========== 交互处理 ==========
  const handleQuickAction = useCallback((action: string) => {
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: getQuickActionText(action),
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);
    
    // 模拟AI响应
    setTimeout(() => {
      const response = generateAIResponse(action);
      setMessages(prev => [...prev, response]);
      setIsTyping(false);
      
      // 触发GUI操作
      if (onGuiAction) {
        onGuiAction(action, response.card?.data);
      }
    }, 1000);
  }, [onGuiAction]);

  const getQuickActionText = (action: string): string => {
    const texts: Record<string, string> = {
      recommend: '推荐今日精选',
      create: '我想创作一首新歌',
      hot: '看看热门挑战',
      story: '浏览音乐故事',
      similar: '推荐相似的歌',
      style: '换个音乐风格',
      lyrics: '帮我写歌词',
      preview: '试听当前效果'
    };
    return texts[action] || action;
  };

  const generateAIResponse = (action: string): AIMessage => {
    let content = '';
    let card: InteractiveCard | undefined;
    
    switch (action) {
      case 'recommend':
        content = '为你精选了今日最动听的音乐，每一首都是精心挑选的珍品~';
        card = {
          type: 'music_recommendation',
          data: {
            title: '今日精选',
            songs: [
              { id: 1, title: '夏日微风', artist: '小明的心情', mood: 'happy', plays: 5234 },
              { id: 2, title: '深夜地铁站', artist: '城市夜归人', mood: 'calm', plays: 3421 },
              { id: 3, title: '雨后彩虹', artist: '梦想家小李', mood: 'excited', plays: 8923 }
            ]
          },
          actions: [
            { label: '全部播放', action: 'play_all', primary: true },
            { label: '收藏歌单', action: 'save_playlist' }
          ]
        };
        break;
        
      case 'create':
        content = '让我们开始创作吧！先告诉我，这首歌想表达什么样的情感？';
        card = {
          type: 'creation_params',
          data: {
            moods: ['欢快', '抒情', '激昂', '忧伤', '神秘'],
            styles: ['流行', '摇滚', '民谣', '电子', '爵士'],
            instruments: ['钢琴', '吉他', '小提琴', '鼓', '合成器']
          },
          actions: [
            { label: '开始创作', action: 'start_creation', primary: true }
          ]
        };
        break;
        
      case 'lyrics':
        content = '基于你的创作主题，我为你准备了几个版本的歌词：';
        card = {
          type: 'lyrics_card',
          data: {
            versions: [
              { id: 1, title: '版本A - 诗意', lyrics: '晨光穿过窗帘的缝隙\n洒在昨夜未完的梦里...' },
              { id: 2, title: '版本B - 直白', lyrics: '每个清晨醒来第一眼\n都想看见你的笑脸...' }
            ]
          },
          actions: [
            { label: '使用版本A', action: 'use_lyrics_a' },
            { label: '使用版本B', action: 'use_lyrics_b' },
            { label: '重新生成', action: 'regenerate' }
          ]
        };
        break;
        
      default:
        content = '好的，正在为你处理...';
    }
    
    return {
      id: Date.now().toString(),
      type: 'ai',
      content,
      timestamp: new Date(),
      card,
      suggestions: getContextualSuggestions()
    };
  };

  const handleSend = useCallback(() => {
    if (!inputText.trim()) return;
    
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);
    
    // 模拟AI响应
    setTimeout(() => {
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `收到！"${inputText}" - 让我为你寻找最合适的音乐...`,
        timestamp: new Date(),
        suggestions: ['继续', '换一个', '更多选项']
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  }, [inputText]);

  // ========== Peek-mode AI ribbon logic (copy from MusicHome) ==========
  const updateAiTalk = useCallback(() => {
    const hour = new Date().getHours();
    let talk: AiTalk;
    if (hour >= 6 && hour < 12) {
      talk = {
        message: '早呀，今天想听点什么？要不要来点元气歌单？',
        quickReplies: [
          { text: '元气打工日常', action: 'morning_energy' },
          { text: '清新舒缓', action: 'morning_calm' }
        ],
        mood: 'happy'
      };
    } else if (hour >= 22 || hour < 6) {
      talk = {
        message: '夜深了，我给你播点轻柔的旋律，慢慢放松下来~',
        quickReplies: [
          { text: '助眠白噪音', action: 'sleep_noise' },
          { text: '小夜曲', action: 'night_serenade' }
        ],
        mood: 'calm'
      };
    } else if (hour >= 12 && hour < 14) {
      talk = {
        message: '午休时间，来点舒缓的音乐吧。',
        quickReplies: [
          { text: '放松一下', action: 'play_relax' },
          { text: '提提神', action: 'play_energetic' }
        ],
        mood: 'calm'
      };
    } else {
      talk = {
        message: '我在呢，随时可以帮你找歌、写歌、做灵感～',
        quickReplies: [
          { text: '推荐热门', action: 'recommend' },
          { text: '帮我写词', action: 'lyrics' }
        ],
        mood: 'curious'
      };
    }
    setCurrentAiTalk(talk);
    if (talk.mood) setCurrentMood(talk.mood);
  }, []);

  useEffect(() => {
    if (!isPeekMode && !ribbonMode) return;
    updateAiTalk();
    setAiTalkMessage('');
    let index = 0;
    const text = currentAiTalk.message || '';
    const typeChar = () => {
      const t = currentAiTalk.message || '';
      if (index < t.length) {
        setAiTalkMessage(t.substring(0, index + 1));
        index++;
        typingTimer.current = setTimeout(typeChar, 50);
      }
    };
    typingTimer.current = setTimeout(typeChar, 300);
    return () => {
      if (typingTimer.current) clearTimeout(typingTimer.current);
    };
  }, [isPeekMode, ribbonMode, updateAiTalk, currentAiTalk.message]);

  const handleQuickReply = useCallback((reply: AiQuickReply) => {
    if (ribbonMode) {
      // 在丝带模式下，触发快速回复后展开完整聊天
      const userMessage: AIMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: reply.text,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, userMessage]);
      onRibbonToggle?.(false);
      onGuiAction?.('open_full_chat', { initialMessage: userMessage });
    } else {
      // 在聊天模式下，正常处理快速回复
      const userMessage: AIMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: reply.text,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, userMessage]);
      setIsPeekMode(false);
      setIsTyping(true);
      setTimeout(() => {
        const aiResponse: AIMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: '好的，我来为你处理…',
          timestamp: new Date(),
          suggestions: getContextualSuggestions()
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsTyping(false);
      }, 800);
    }
  }, [ribbonMode, onGuiAction]);

  // ========== 魔术指针功能 ==========
  const handleMagicPointer = useCallback(() => {
    setIsMagicPointerActive(true);
    setIsPeekMode(true);
    
    // 监听点击事件
    const handleElementClick = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      
      const target = e.target as HTMLElement;
      const label = target.textContent || target.getAttribute('aria-label') || '这个元素';
      
      setSelectedElement({
        element: target,
        label: label.substring(0, 30),
        type: target.tagName.toLowerCase()
      });
      
      setInputText(`关于 "${label}"...`);
      setIsMagicPointerActive(false);
      setIsPeekMode(false);
      
      // 高亮效果
      target.style.outline = '2px solid #8B5CF6';
      setTimeout(() => {
        target.style.outline = '';
      }, 2000);
    };
    
    // 添加全局点击监听
    setTimeout(() => {
      document.addEventListener('click', handleElementClick, { once: true });
    }, 100);
  }, []);



  // ========== 丝带模式切换 ==========
  const toggleRibbonMode = useCallback(() => {
    if (ribbonMode) {
      // 在丝带模式下，切换丝带的显示/隐藏
      onRibbonToggle?.(!showRibbon);
    } else {
      // 在聊天模式下，切换窥探模式
      setIsPeekMode(!isPeekMode);
    }
    navigator.vibrate?.(30);
  }, [ribbonMode, showRibbon, isPeekMode, onRibbonToggle]);

  // ========== 渲染 ==========
  if (!isOpen && !ribbonMode) {
    return null;
  }

  // 丝带模式渲染
  if (ribbonMode) {
    return (
      <div
        className={`${allowOverlay ? 'absolute' : 'fixed'} top-0 left-0 right-0 z-50 transition-all duration-300`}
        style={{ maxWidth: '430px', margin: '0 auto' }}
      >
        {showRibbon && (
          <div className="relative pt-12 px-6 pb-3 z-20 transition-all duration-300" onDoubleClick={() => onRibbonToggle?.(false)}>
            <div
              className="bg-white/5 backdrop-blur-lg rounded-2xl p-3 border border-white/10 cursor-pointer hover:bg-white/10 transition-all pointer-events-auto"
              onClick={(e) => {
                e.stopPropagation();
                if (isPeekMode) {
                  // 在窥探模式下，点击丝带展开完整聊天
                  setIsPeekMode(false);
                } else if (ribbonMode) {
                  // 在丝带模式下，点击丝带展开完整聊天
                  onRibbonToggle?.(false);
                  onGuiAction?.('open_full_chat');
                }
              }}
            >
              <div className="flex items-start gap-2.5">
                <div className={`w-8 h-8 rounded-full bg-gradient-to-br ${moodColors[currentMood].primary} flex items-center justify-center flex-shrink-0 overflow-hidden`}>
                  <img
                    src={xiaoxianImage}
                    alt="小弦"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.currentTarget as HTMLImageElement).style.display = 'none';
                      e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class=\"w-4 h-4 text-white\">✨</div>');
                    }}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-0.5">
                    <span className="text-white font-medium text-xs">小弦</span>
                    <span className="text-xs text-white/40">AI音乐伙伴</span>
                  </div>
                  <p className="text-white/80 text-xs leading-relaxed">
                    {aiTalkMessage}
                    {aiTalkMessage.length < (currentAiTalk.message?.length || 0) && (
                      <span className="inline-block w-0.5 h-3 ml-0.5 bg-white/60 animate-blink" />
                    )}
                  </p>
                  {currentAiTalk.quickReplies && aiTalkMessage === currentAiTalk.message && (
                    <div className="flex gap-2 mt-2 animate-fade-in">
                      {currentAiTalk.quickReplies.map((reply, index) => (
                        <button
                          key={index}
                          className="px-2 py-1 bg-white/10 hover:bg-white/15 rounded-full text-xs text-white/70 transition-all"
                          onClick={(e) => {
                            e.stopPropagation(); // 阻止冒泡到父容器的点击事件
                            handleQuickReply(reply);
                          }}
                        >
                          {reply.text}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="mt-3 flex items-center gap-1 text-white/40 text-xs">
                <span>💡</span>
                <span>双击丝带可收起，点击展开完整聊天</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // 聊天模式渲染
  return (
    <div
      className={`${isPeekMode ? 'absolute top-0 left-0 right-0' : 'absolute inset-0'} z-[60] transition-all duration-300 ${
        isPeekMode ? 'pointer-events-none' : 'pointer-events-auto'
      }`}
      style={{ maxWidth: '430px', margin: '0 auto' }}
    >
      {/* 背景遮罩 - 窥视模式下完全移除 */}
      {!isPeekMode && (
        <div
          className="absolute inset-0 transition-all duration-500 rounded-[2.8rem] bg-black/60 backdrop-blur-md"
          onClick={onClose}
        />
      )}

      {/* 主体内容 */}
      <div
        className={`absolute inset-x-0 bottom-0 rounded-[2.8rem] transition-all duration-500 ${
          isPeekMode
            ? 'top-0 bg-transparent pointer-events-auto'
            : 'top-0 bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-xl flex flex-col pb-8 pointer-events-auto'
        }`}
        onClick={(e) => isPeekMode ? undefined : e.stopPropagation()}
      >
        {isPeekMode && (
          <div className="relative pt-14 px-6 pb-3 z-20 transition-all duration-300 pointer-events-auto" onDoubleClick={() => setIsPeekMode(false)}>
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10 pointer-events-auto">
              <div className="flex items-start gap-3 mb-3">
                <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${moodColors[currentMood].primary} flex items-center justify-center flex-shrink-0 overflow-hidden`}>
                  <img 
                    src={xiaoxianImage} 
                    alt="小弦" 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.currentTarget as HTMLImageElement).style.display = 'none';
                      e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class=\"w-5 h-5 text-white\">?</div>');
                    }}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white font-medium text-sm">小弦</span>
                    <span className="text-xs text-white/40">AI音乐助手</span>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed">
                    {aiTalkMessage}
                    {aiTalkMessage.length < (currentAiTalk.message?.length || 0) && (
                      <span className="inline-block w-1 h-4 ml-1 bg-white/60 animate-blink" />
                    )}
                  </p>
                </div>
              </div>
              {currentAiTalk.quickReplies && aiTalkMessage === currentAiTalk.message && (
                <div className="flex gap-2 mt-3 animate-fade-in">
                  {currentAiTalk.quickReplies.map((reply, index) => (
                    <button
                      key={index}
                      className="px-4 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-white text-sm transition-all active:scale-95"
                      onClick={() => handleQuickReply(reply)}
                    >
                      {reply.text}
                    </button>
                  ))}
                </div>
              )}
              <div className="mt-3 flex items-center gap-1 text-white/40 text-xs">
                <span>💡</span>
                <span>双击主页小弦气泡可展开或收起</span>
              </div>
            </div>
          </div>
        )}
        
        {/* 顶部区域 - 上下文感知中心 */}
        {!isPeekMode && (
          <div className="relative px-6 pt-4 pb-3 border-b border-white/10">
            {/* 拖动指示条 */}
            <div className="absolute top-2 left-1/2 -translate-x-1/2 w-12 h-1 bg-white/30 rounded-full" />
            
            {/* 主内容区 */}
            <div className="mt-4 mb-3 flex items-center justify-between">
              {/* 左侧：上下文锚点 */}
              {currentAnchor && (
                <div className="flex items-center gap-3 flex-1">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center">
                    {currentAnchor.icon && <currentAnchor.icon className="w-5 h-5 text-purple-400" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium text-sm truncate">{currentAnchor.title}</p>
                    {currentAnchor.subtitle && (
                      <p className="text-white/50 text-xs truncate">{currentAnchor.subtitle}</p>
                    )}
                  </div>
                </div>
              )}
              
              {/* 右侧：任务中心入口 */}
              <button
                onClick={() => setShowTaskPanel(true)}
                className="relative flex items-center gap-2 px-3 py-1.5 bg-white/10 hover:bg-white/15 rounded-full transition-all active:scale-95"
              >
                {/* 任务图标和数量 */}
                <div className="flex items-center gap-1">
                  {taskStats.notifications > 0 && (
                    <div className="flex items-center">
                      <MessageCircle className="w-4 h-4 text-blue-400" />
                      <span className="ml-1 text-xs text-blue-400 font-medium">{taskStats.notifications}</span>
                    </div>
                  )}
                  {taskStats.generations > 0 && (
                    <div className="flex items-center ml-1">
                      <Zap className="w-4 h-4 text-purple-400" />
                      <span className="ml-1 text-xs text-purple-400 font-medium">{taskStats.generations}</span>
                    </div>
                  )}
                  {taskStats.total === 0 && (
                    <span className="text-xs text-white/50">无任务</span>
                  )}
                </div>
                
                {/* 动态呼吸灯效果 */}
                {taskStats.total > 0 && (
                  <div className="absolute -top-1 -right-1 w-3 h-3">
                    <span className="absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75 animate-ping" />
                    <span className="relative inline-flex rounded-full h-3 w-3 bg-purple-500" />
                  </div>
                )}
              </button>
            </div>
            
            {/* 任务进度条（如果有进行中的任务） */}
            {currentAnchor?.progress !== undefined && (
              <div className="mt-2 flex items-center gap-2">
                <div className="flex-1 h-1 bg-white/10 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all duration-300"
                    style={{ width: `${currentAnchor.progress}%` }}
                  />
                </div>
                <span className="text-xs text-white/50">{currentAnchor.progress}%</span>
              </div>
            )}
          </div>
        )}
        
        {/* 中间对话区域 */}
        {!isPeekMode && (
        <div
          ref={chatScrollRef}
          className={`overflow-y-auto transition-all duration-300 scrollbar-hide ${
            isPeekMode ? 'h-32' : 'flex-1'
          }`}
          style={{
            height: isPeekMode ? '8rem' : 'auto',
            maxHeight: isPeekMode ? '8rem' : 'none',
            scrollbarWidth: 'none', /* Firefox */
            msOverflowStyle: 'none' /* IE and Edge */
          }}
        >
          <div className="px-6 py-4 space-y-4">
            {messages.map(message => (
              <MessageBubble key={message.id} message={message} onAction={handleQuickAction} />
            ))}
            
            {isTyping && (
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div className="bg-white/10 backdrop-blur px-4 py-2 rounded-2xl">
                  <TypingIndicator />
                </div>
              </div>
            )}
          </div>
        </div>
        )}
        
        {/* 底部输入区域 */}
        {!isPeekMode && (
        <div className="border-t border-white/10 px-4 py-3 bg-black/30">
          {/* 快捷操作栏 */}
          {!isPeekMode && (
            <div className="mb-3 flex gap-2 overflow-x-auto no-scrollbar">
              {quickActions.map(action => (
                <button
                  key={action.id}
                  onClick={action.action}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm whitespace-nowrap transition-all ${
                    action.highlight
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30'
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  <action.icon className="w-3.5 h-3.5" />
                  <span>{action.label}</span>
                </button>
              ))}
            </div>
          )}
          
          {/* 输入框 */}
          <div className="flex items-center gap-2">
            {/* 魔术指针 */}
            <button
              onClick={handleMagicPointer}
              className={`w-10 h-10 rounded-full flex items-center justify-center transition-all ${
                isMagicPointerActive
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 animate-pulse'
                  : 'bg-white/10 hover:bg-white/20'
              }`}
              title="魔术指针 - 点击界面元素"
            >
              <MousePointer className="w-5 h-5 text-white" />
            </button>
            
            {/* 主输入框 */}
            <div className="flex-1 bg-white/10 backdrop-blur rounded-full px-4 py-2 flex items-center gap-2">
              <input
                ref={inputRef}
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSend()}
                placeholder={selectedElement ? `关于"${selectedElement.label}"...` : "你想说什么...(长按可语音输入)"}
                className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
              />

              {/* 图片按钮 */}
              <button
                onClick={() => {
                  // Mock 图片功能
                  console.log('图片功能待实现');
                  // 这里可以添加图片选择逻辑
                }}
                className="w-8 h-8 rounded-full flex items-center justify-center transition-all bg-white/10 hover:bg-white/20"
                title="发送图片"
              >
                <Image className="w-4 h-4 text-white/70" />
              </button>

              {/* 发送按钮 */}
              <button
                onClick={handleSend}
                disabled={!inputText.trim()}
                className="w-8 h-8 rounded-full flex items-center justify-center transition-all bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-4 h-4 text-white/70" />
              </button>
            </div>
            
            {/* 丝带模式切换按钮 */}
            <button
              onClick={toggleRibbonMode}
              className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all hover:scale-105 active:scale-95"
              title="切换丝带模式"
            >
              <Minimize2 className="w-5 h-5 text-white/70" />
            </button>
          </div>
          
          {/* 魔术指针提示 */}
          {isMagicPointerActive && (
            <div className="mt-2 px-2 py-1 bg-purple-500/20 rounded-lg">
              <p className="text-xs text-purple-300 text-center animate-pulse">
                点击页面上的任何元素来引用它...
              </p>
            </div>
          )}
        </div>
        )}
        
        {/* Home Indicator */}
        {!isPeekMode && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        )}
      </div>

      {/* 任务面板 - 从顶部滑入 */}
      {showTaskPanel && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] animate-fade-in"
            onClick={() => setShowTaskPanel(false)}
          />

          {/* 任务面板主体 */}
          <div
            className="fixed top-0 left-0 right-0 z-[61] animate-slide-down"
            style={{ maxWidth: '430px', margin: '0 auto' }}
          >
            <div className="bg-gray-900/95 backdrop-blur-xl rounded-b-[2rem] border border-white/10 shadow-2xl">
              {/* 面板头部 */}
              <div className="px-6 pt-6 pb-4 border-b border-white/10">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-white text-lg font-semibold">任务中心</h3>
                  <button
                    onClick={() => setShowTaskPanel(false)}
                    className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center transition-all"
                  >
                    <X className="w-4 h-4 text-white/70" />
                  </button>
                </div>
                <p className="text-white/50 text-sm">
                  {taskStats.total > 0 ? `${taskStats.total} 个待处理任务` : '暂无新任务'}
                </p>
              </div>

              {/* 任务分类标签 */}
              <div className="px-6 py-3 flex gap-2">
                {taskStats.notifications > 0 && (
                  <div className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-500/20 rounded-full">
                    <MessageCircle className="w-3.5 h-3.5 text-blue-400" />
                    <span className="text-xs text-blue-400">{taskStats.notifications} 条通知</span>
                  </div>
                )}
                {taskStats.generations > 0 && (
                  <div className="flex items-center gap-1.5 px-3 py-1.5 bg-purple-500/20 rounded-full">
                    <Zap className="w-3.5 h-3.5 text-purple-400" />
                    <span className="text-xs text-purple-400">{taskStats.generations} 个生成任务</span>
                  </div>
                )}
              </div>

              {/* 任务列表 */}
              <div className="max-h-[60vh] overflow-y-auto px-6 pb-6">
                {tasks.length > 0 ? (
                  <div className="space-y-3">
                    {tasks.map((task, index) => (
                      <div
                        key={task.id}
                        className="bg-white/5 hover:bg-white/10 rounded-xl p-4 transition-all cursor-pointer group animate-fade-in"
                        style={{ animationDelay: `${index * 50}ms` }}
                        onClick={() => {
                          task.action?.();
                          setShowTaskPanel(false);
                        }}
                      >
                        <div className="flex items-start gap-3">
                          {/* 任务图标 */}
                          <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${task.color} flex items-center justify-center flex-shrink-0`}>
                            <task.icon className={`w-5 h-5 text-white ${task.type === 'generation' && task.status === 'pending' ? 'animate-spin' : ''}`} />
                          </div>

                          {/* 任务内容 */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1">
                                <p className="text-white text-sm font-medium">{task.title}</p>
                                {task.subtitle && (
                                  <p className="text-white/60 text-xs mt-0.5">{task.subtitle}</p>
                                )}
                              </div>
                              <span className="text-white/40 text-xs whitespace-nowrap">
                                {formatTime(task.timestamp)}
                              </span>
                            </div>

                            {/* 任务状态 */}
                            {task.status === 'completed' && (
                              <div className="mt-2 flex items-center gap-1">
                                <div className="w-4 h-4 bg-green-500/20 rounded-full flex items-center justify-center">
                                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                                </div>
                                <span className="text-xs text-green-400">已完成</span>
                              </div>
                            )}
                          </div>

                          {/* 操作提示 */}
                          <ChevronRight className="w-4 h-4 text-white/30 group-hover:text-white/60 transition-all" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <div className="w-16 h-16 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Trophy className="w-8 h-8 text-white/30" />
                    </div>
                    <p className="text-white/50 text-sm">所有任务已完成！</p>
                  </div>
                )}
              </div>

              {/* 底部操作栏 */}
              {tasks.length > 0 && (
                <div className="px-6 py-3 border-t border-white/10">
                  <button
                    className="w-full py-2 bg-white/10 hover:bg-white/15 rounded-xl text-white/70 text-sm transition-all"
                    onClick={() => {
                      setTasks([]);
                      setShowTaskPanel(false);
                    }}
                  >
                    清空所有任务
                  </button>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// ==================== 子组件 ====================

// 消息气泡组件
const MessageBubble: React.FC<{
  message: AIMessage;
  onAction: (action: string) => void;
}> = ({ message, onAction }) => {
  return (
    <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
      {/* AI头像 - 左侧 */}
      {message.type === 'ai' && (
        <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
          <img
            src={xiaoxianImage}
            alt="小弦"
            className="w-full h-full object-cover rounded-full"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
              e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class="text-white text-xs">✨</div>');
            }}
          />
        </div>
      )}

      <div className={`max-w-[80%] ${message.type === 'user' ? 'items-end' : 'items-start'} flex flex-col gap-2`}>
        {/* 文本内容 */}
        <div className={`px-4 py-2.5 rounded-2xl ${
          message.type === 'user'
            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
            : 'bg-white/10 backdrop-blur text-white'
        }`}>
          <p className="text-sm leading-relaxed">{message.content}</p>
        </div>
        
        {/* 交互卡片 */}
        {message.card && (
          <InteractiveCardComponent card={message.card} onAction={onAction} />
        )}
        
        {/* 建议选项 */}
        {message.suggestions && (
          <div className="flex gap-2 flex-wrap">
            {message.suggestions.map((suggestion, i) => (
              <button
                key={i}
                onClick={() => onAction(suggestion)}
                className="px-3 py-1 bg-white/10 hover:bg-white/20 backdrop-blur rounded-full text-xs text-white/80 transition-all hover:scale-105 active:scale-95"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 用户头像 - 右侧 */}
      {message.type === 'user' && (
        <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full flex items-center justify-center ml-2 flex-shrink-0">
          <img
            src="/images/Musician.png"
            alt="用户"
            className="w-full h-full object-cover rounded-full"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
              e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class="text-white text-xs">👤</div>');
            }}
          />
        </div>
      )}
    </div>
  );
};

// 交互卡片组件
const InteractiveCardComponent: React.FC<{
  card: InteractiveCard;
  onAction: (action: string) => void;
}> = ({ card, onAction }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  switch (card.type) {
    case 'music_recommendation':
      return (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 w-full">
          <h3 className="text-white font-medium text-sm mb-3 flex items-center gap-2">
            <Music className="w-4 h-4 text-purple-400" />
            {card.data.title}
          </h3>
          
          {/* 横向滚动的歌曲列表 */}
          <div className="flex gap-3 overflow-x-auto no-scrollbar pb-2">
            {card.data.songs.map((song: any, i: number) => (
              <div 
                key={song.id}
                className="flex-shrink-0 w-32 bg-white/10 rounded-xl p-3 hover:bg-white/20 transition-all cursor-pointer"
                onClick={() => onAction(`play_${song.id}`)}
              >
                <div className="w-full aspect-square bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-lg mb-2 flex items-center justify-center">
                  <Play className="w-8 h-8 text-white/50" />
                </div>
                <p className="text-white text-xs font-medium truncate">{song.title}</p>
                <p className="text-white/50 text-xs truncate">{song.artist}</p>
                {song.plays && (
                  <p className="text-white/40 text-xs mt-1">{(song.plays / 1000).toFixed(1)}k播放</p>
                )}
              </div>
            ))}
          </div>
          
          {/* 操作按钮 */}
          {card.actions && (
            <div className="flex gap-2 mt-3">
              {card.actions.map((action, i) => (
                <button
                  key={i}
                  onClick={() => onAction(action.action)}
                  className={`px-4 py-1.5 rounded-full text-xs font-medium transition-all hover:scale-105 active:scale-95 ${
                    action.primary
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
      );
      
    case 'creation_params':
      return (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 w-full">
          <h3 className="text-white font-medium text-sm mb-3 flex items-center gap-2">
            <Layers className="w-4 h-4 text-purple-400" />
            创作参数
          </h3>
          
          {/* 情绪选择 */}
          <div className="mb-3">
            <p className="text-white/70 text-xs mb-2">选择情绪</p>
            <div className="flex gap-2 flex-wrap">
              {card.data.moods.map((mood: string, i: number) => (
                <button
                  key={i}
                  onClick={() => setSelectedIndex(i)}
                  className={`px-3 py-1 rounded-full text-xs transition-all ${
                    selectedIndex === i
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  {mood}
                </button>
              ))}
            </div>
          </div>
          
          {/* 风格选择 */}
          <div className="mb-3">
            <p className="text-white/70 text-xs mb-2">音乐风格</p>
            <div className="flex gap-2 flex-wrap">
              {card.data.styles.map((style: string, i: number) => (
                <button
                  key={i}
                  className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs text-white/70 transition-all"
                >
                  {style}
                </button>
              ))}
            </div>
          </div>
          
          {/* 乐器选择 */}
          <div className="mb-3">
            <p className="text-white/70 text-xs mb-2">主要乐器</p>
            <div className="flex gap-2 overflow-x-auto no-scrollbar">
              {card.data.instruments.map((instrument: string, i: number) => (
                <div
                  key={i}
                  className="flex-shrink-0 w-16 h-16 bg-white/10 hover:bg-white/20 rounded-lg flex flex-col items-center justify-center gap-1 cursor-pointer transition-all"
                >
                  <Music className="w-6 h-6 text-white/50" />
                  <span className="text-[10px] text-white/70">{instrument}</span>
                </div>
              ))}
            </div>
          </div>
          
          {/* 操作按钮 */}
          {card.actions && (
            <div className="flex gap-2">
              {card.actions.map((action, i) => (
                <button
                  key={i}
                  onClick={() => onAction(action.action)}
                  className={`flex-1 px-4 py-2 rounded-full text-xs font-medium transition-all hover:scale-105 active:scale-95 ${
                    action.primary
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
      );
      
    case 'lyrics_card':
      return (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 w-full">
          <h3 className="text-white font-medium text-sm mb-3 flex items-center gap-2">
            <PenTool className="w-4 h-4 text-purple-400" />
            歌词灵感
          </h3>
          
          {/* 歌词版本 */}
          <div className="space-y-3">
            {card.data.versions.map((version: any) => (
              <div 
                key={version.id}
                className="bg-white/5 rounded-lg p-3 hover:bg-white/10 transition-all cursor-pointer"
              >
                <p className="text-purple-300 text-xs font-medium mb-2">{version.title}</p>
                <p className="text-white/70 text-xs leading-relaxed whitespace-pre-line">
                  {version.lyrics}
                </p>
              </div>
            ))}
          </div>
          
          {/* 操作按钮 */}
          {card.actions && (
            <div className="flex gap-2 mt-3">
              {card.actions.map((action, i) => (
                <button
                  key={i}
                  onClick={() => onAction(action.action)}
                  className="flex-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-xs text-white/70 transition-all"
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
      );

case 'daily_highlight':
  return (
    <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-xl rounded-2xl p-4 w-full border border-purple-500/20">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-lg flex items-center justify-center animate-pulse">
            <Flame className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-white font-bold text-sm">{card.data.title}</h3>
            <p className="text-white/60 text-xs">{card.data.date}</p>
          </div>
        </div>
        <div className="px-2 py-1 bg-red-500/20 rounded-full">
          <span className="text-red-400 text-xs font-medium">今日必听</span>
        </div>
      </div>

      {/* 精选歌曲列表 */}
      <div className="space-y-3">
        {card.data.songs.map((song: any, i: number) => (
          <div
            key={song.id}
            className="group relative bg-white/5 hover:bg-white/10 rounded-xl p-3 transition-all cursor-pointer"
            onClick={() => onAction(`play_${song.id}`)}
          >
            <div className="flex items-center gap-3">
              {/* 排名标识 */}
              <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-white/10 rounded-lg flex items-center justify-center font-bold text-white">
                {i + 1}
              </div>

              {/* 歌曲封面 */}
              <div className={`w-12 h-12 bg-gradient-to-br ${song.coverGradient} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                <Music className="w-6 h-6 text-white/80" />
              </div>

              {/* 歌曲信息 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p className="text-white text-sm font-medium truncate">{song.title}</p>
                  {song.trending && (
                    <span className="px-1.5 py-0.5 bg-red-500/20 rounded text-red-400 text-xs animate-pulse">
                      HOT
                    </span>
                  )}
                </div>
                <p className="text-white/50 text-xs truncate">{song.artist}</p>
              </div>

              {/* 数据统计 */}
              <div className="text-right">
                <div className="flex items-center gap-1 text-white/60">
                  <Heart className="w-3 h-3" />
                  <span className="text-xs">{song.likes}</span>
                </div>
                <p className="text-white/40 text-xs">{(song.plays / 1000).toFixed(1)}k播放</p>
              </div>

              {/* 播放按钮 */}
              <button className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all">
                <Play className="w-4 h-4 text-white" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* 操作按钮 */}
      {card.actions && (
        <div className="flex gap-2 mt-4">
          {card.actions.map((action, i) => (
            <button
              key={i}
              onClick={() => onAction(action.action)}
              className={`flex-1 px-4 py-2 rounded-full text-sm font-medium transition-all hover:scale-105 active:scale-95 ${
                action.primary
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-500/30'
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );

case 'creation_activity':
  const timeLeft = card.data.endTime ? new Date(card.data.endTime).getTime() - Date.now() : 0;
  const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
  const minutesLeft = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

  return (
    <div className="relative bg-gradient-to-br from-purple-600/20 via-pink-600/20 to-orange-600/20 backdrop-blur-xl rounded-2xl p-4 w-full border border-white/20 overflow-hidden">
      {/* 背景动画装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />

      <div className="relative z-10">
        {/* 头部信息 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Trophy className="w-5 h-5 text-yellow-400" />
              <h3 className="text-white font-bold text-base">创作挑战：{card.data.title}</h3>
            </div>
            <p className="text-white/70 text-xs">{card.data.description}</p>
          </div>

          {/* 热度指示器 */}
          <div className="flex flex-col items-center">
            <div className="relative w-12 h-12">
              <svg className="w-12 h-12 transform -rotate-90">
                <circle cx="24" cy="24" r="20" stroke="rgba(255,255,255,0.1)" strokeWidth="4" fill="none" />
                <circle
                  cx="24" cy="24" r="20"
                  stroke="url(#hotGradient)"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray={`${card.data.hotLevel * 1.26} 126`}
                  strokeLinecap="round"
                />
                <defs>
                  <linearGradient id="hotGradient">
                    <stop offset="0%" stopColor="#f59e0b" />
                    <stop offset="100%" stopColor="#ef4444" />
                  </linearGradient>
                </defs>
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <Flame className="w-5 h-5 text-orange-400 animate-pulse" />
              </div>
            </div>
            <span className="text-xs text-orange-400 mt-1">热度{card.data.hotLevel}%</span>
          </div>
        </div>

        {/* 标签 */}
        <div className="flex gap-2 mb-3">
          {card.data.tags.map((tag: string, i: number) => (
            <span key={i} className="px-2 py-1 bg-white/10 rounded-full text-xs text-white/70">
              #{tag}
            </span>
          ))}
          <span className="px-2 py-1 bg-green-500/20 rounded-full text-xs text-green-400">
            难度：{card.data.difficulty}
          </span>
        </div>

        {/* 奖励展示 */}
        <div className="bg-black/20 rounded-xl p-3 mb-3">
          <p className="text-white/70 text-xs mb-2">🎁 奖励池</p>
          <div className="grid grid-cols-4 gap-2">
            <div className="text-center">
              <div className="text-2xl mb-1">{card.data.prize.first.badge}</div>
              <p className="text-yellow-400 text-sm font-bold">¥{card.data.prize.first.amount}</p>
              <p className="text-white/40 text-xs">冠军</p>
            </div>
            <div className="text-center">
              <div className="text-xl mb-1">{card.data.prize.second.badge}</div>
              <p className="text-gray-300 text-sm font-bold">¥{card.data.prize.second.amount}</p>
              <p className="text-white/40 text-xs">亚军</p>
            </div>
            <div className="text-center">
              <div className="text-lg mb-1">{card.data.prize.third.badge}</div>
              <p className="text-orange-400 text-sm font-bold">¥{card.data.prize.third.amount}</p>
              <p className="text-white/40 text-xs">季军</p>
            </div>
            <div className="text-center">
              <div className="text-base mb-1">{card.data.prize.participation.badge}</div>
              <p className="text-purple-400 text-sm font-bold">{card.data.prize.participation.amount}</p>
              <p className="text-white/40 text-xs">参与奖</p>
            </div>
          </div>
        </div>

        {/* 参与信息 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4 text-blue-400" />
              <span className="text-white text-sm font-medium">{card.data.participants}</span>
              <span className="text-white/50 text-xs">人参与</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4 text-orange-400" />
              <span className="text-white text-sm font-medium">{hoursLeft}h {minutesLeft}m</span>
              <span className="text-white/50 text-xs">剩余</span>
            </div>
          </div>
        </div>

        {/* 当前TOP作品 */}
        {card.data.topWorks && card.data.topWorks.length > 0 && (
          <div className="bg-white/5 rounded-lg p-2 mb-3">
            <p className="text-white/60 text-xs mb-1">🔥 当前领先</p>
            <div className="space-y-1">
              {card.data.topWorks.map((work: any, i: number) => (
                <div key={i} className="flex items-center justify-between">
                  <span className="text-white/80 text-xs">#{i+1} {work.title}</span>
                  <span className="text-white/50 text-xs">{work.likes} ❤️</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {card.actions && (
          <div className="flex gap-2">
            {card.actions.map((action, i) => (
              <button
                key={i}
                onClick={() => onAction(action.action)}
                className={`flex-1 px-4 py-2.5 rounded-full text-sm font-medium transition-all hover:scale-105 active:scale-95 ${
                  action.primary
                    ? 'bg-gradient-to-r from-orange-500 to-pink-500 text-white shadow-lg shadow-orange-500/30 animate-pulse'
                    : 'bg-white/10 text-white/70 hover:bg-white/20'
                }`}
              >
                {action.label}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );

    default:
      return null;
  }
};

// 打字指示器
const TypingIndicator: React.FC = () => (
  <div className="flex gap-1">
    <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
    <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
    <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
  </div>
);

// ==================== 样式注入 ====================
const injectStyles = () => {
  const styleId = 'ai-chat-styles';
  if (document.getElementById(styleId)) return;
  
  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-4px); }
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    @keyframes fade-in {
      from { opacity: 0; transform: translateY(4px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    @keyframes slide-down {
      from {
        transform: translateY(-100%);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    @keyframes ping {
      75%, 100% {
        transform: scale(2);
        opacity: 0;
      }
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .animate-bounce {
      animation: bounce 1.4s infinite;
    }

    .animate-pulse {
      animation: pulse 2s infinite;
    }
    .animate-fade-in {
      animation: fade-in 0.5s ease-out;
    }
    .animate-blink {
      animation: blink 1s infinite;
    }

    .animate-slide-down {
      animation: slide-down 0.3s ease-out;
    }

    .animate-ping {
      animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }
    
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }
    
    .no-scrollbar {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  `;
  document.head.appendChild(style);
};

// 组件挂载时注入样式
if (typeof document !== 'undefined') {
  injectStyles();
}

export default AIChat;
