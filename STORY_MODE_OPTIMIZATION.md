# 故事模式字幕优化方案

## 问题描述
原始的故事模式字幕胶囊打开状态显示了太多文字，用户体验不佳。需要将故事模式的字幕展示方式调整为与音乐模式一致，只高亮当前播放的内容，弱化显示其他内容。

## 解决方案

### 1. 数据结构优化
- **新增故事片段数据**: 创建了 `storySegments` 数组，将长篇故事内容按时间轴分割成多个短片段
- **时间同步**: 每个故事片段都有对应的时间戳，与歌词时间轴保持同步
- **内容精炼**: 每个片段控制在一句话的长度，便于阅读和理解

```typescript
const storySegments = [
  { time: 0, text: '这首歌诞生在一个夏天的傍晚，微风轻拂，夕阳西下。' },
  { time: 4, text: '创作者坐在海边，看着远方的地平线，心中涌起了对生活的无限感慨。' },
  // ... 更多片段
];
```

### 2. 状态管理优化
- **新增状态**: `currentStorySegmentIndex` 跟踪当前故事片段索引
- **动画状态**: `storySegmentChanging` 控制片段切换时的动画效果
- **播放同步**: 只有在视频播放时，故事片段才会跟随时间轴自动切换

### 3. 显示逻辑优化

#### 收起状态
- 只显示当前故事片段的前30个字符
- 添加脉冲动画的指示器
- 平滑的过渡动画

#### 展开状态
- **前一段**: 弱化显示 (40% 透明度)
- **当前段**: 高亮显示，带有渐变指示器
- **后一段**: 弱化显示 (40% 透明度)
- **进度指示器**: 显示当前片段在整个故事中的位置

### 4. 交互体验优化
- **播放同步**: 故事片段只在视频播放时自动切换，暂停时保持当前状态
- **手势控制**: 上下滑动可以手动切换故事片段
- **视觉反馈**: 片段切换时有微妙的缩放和透明度动画
- **一致性**: 音乐模式和故事模式采用相同的UI设计模式

### 5. 视觉增强
- **渐变指示器**: 当前片段下方显示紫色到粉色的渐变线条
- **进度点**: 小圆点显示故事进度，当前片段高亮
- **平滑过渡**: 所有状态变化都有500ms的过渡动画
- **脉冲效果**: 指示器有微妙的脉冲动画，增加生动感

## 技术实现要点

### 时间轴同步
```typescript
// 在播放控制中同时更新歌词和故事片段
const currentStorySegment = storySegments.find((_, index) => {
  const nextSegment = storySegments[index + 1];
  return newTime >= _.time && (!nextSegment || newTime < nextSegment.time);
});
```

### 动画控制
```typescript
// 片段切换时的动画效果
if (newIndex !== currentStorySegmentIndex) {
  setStorySegmentChanging(true);
  setCurrentStorySegmentIndex(newIndex);
  setTimeout(() => setStorySegmentChanging(false), 300);
}
```

### 条件渲染
```typescript
// 只在播放时自动切换故事片段
if (autoPlayStory && contentMode === 'story' && storySegments.length > 1 && isPlaying) {
  // 自动切换逻辑
}
```

## 用户体验改进

1. **信息密度**: 从显示大段文字改为精炼的短句，降低认知负担
2. **视觉层次**: 通过透明度和字体大小区分当前内容和上下文
3. **播放同步**: 故事内容与音乐播放进度完全同步
4. **交互一致**: 故事模式和音乐模式使用相同的交互模式
5. **视觉反馈**: 丰富的动画效果提升使用体验

## 最佳实践

1. **内容分割**: 将长文本按语义和时间合理分割
2. **时间对齐**: 确保故事片段时间与音乐节拍对应
3. **动画节制**: 使用微妙的动画，避免过度干扰
4. **一致性**: 保持不同模式间的UI一致性
5. **性能优化**: 使用CSS过渡而非JavaScript动画

## 结果
- ✅ 故事模式字幕显示方式与音乐模式完全一致
- ✅ 只高亮当前播放的故事片段
- ✅ 弱化显示前后文，提供上下文
- ✅ 只在视频播放时故事内容才跟随滚动
- ✅ 丰富的视觉反馈和动画效果
- ✅ 优秀的用户体验和交互一致性
