import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  Music, Sparkles, Play, Heart, MessageCircle, Share2,
  Home, Pause, SkipForward, Send,
  TrendingUp, Hash, Clock, ChevronRight, Zap, Star,
  Headphones, Gift, Bell, Loader2, X, Flame,
  Mic, MicOff, ChevronUp, Command, Layers,
  ArrowRight, CircleEllipsis, Brain, Plus,
  User, PenTool, Palette, ChevronDown, Sparkle,
  Trophy, Users, Feather, Calendar, Target,
  BookOpen, Smile, Coffee, Moon, Sun
} from 'lucide-react';
import { AIChat } from '../components';

// Import image assets
const SummerFarewell = `${import.meta.env.BASE_URL}images/SummerFarewell.png`;
const MidnightSubway = `${import.meta.env.BASE_URL}images/MidnightSubway.png`;
const MomBirthdaySong = `${import.meta.env.BASE_URL}images/MomBirthdaySong.png`;
const HeavenHavoc = `${import.meta.env.BASE_URL}images/HeavenHavoc.png`;

// ==================== 类型定义 ====================
interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  type: 'note' | 'star' | 'heart' | 'wave';
  rotation: number;
  rotationSpeed: number;
  lifespan: number;
  maxLife: number;
}

interface MoodColor {
  primary: string;
  secondary: string;
  particles: string[];
  glow: string;
  background: string;
  accent: string;
}

interface MusicCard {
  id: string;
  title: string;
  artist: string;
  mood: string;
  plays: number;
  likes: number;
  coverGradient: string;
  duration: string;
  isPlaying?: boolean;
  waveform?: number[];
  tags: string[];
  createdAt: Date;
  storyBrief?: string;
  energy: number;
  resonanceCount?: number;
  remixCount?: number;
  storyAlbum?: {
    id: string;
    name: string;
    episode: number;
    totalEpisodes: number;
  };
}

interface StoryCard {
  id: string;
  title: string;
  author: string;
  story: string;
  musicId: string;
  coverGradient: string;
  mood: string;
  resonanceCount: number;
  createdAt: Date;
  duration: string;
  coverImage?: string;
  storyAlbum?: {
    id: string;
    name: string;
    episode: number;
    totalEpisodes: number;
  };
}

interface StoryAlbum {
  id: string;
  name: string;
  totalEpisodes: number;
  currentEpisode?: number;
  coverImage?: string;
  genre: 'personal' | 'classic' | 'fantasy' | 'romance' | 'adventure';
}

interface ChainChallenge {
  id: string;
  title: string;
  participants: number;
  segments: number;
  coverGradient: string;
  deadline: Date;
  reward: string;
}

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  isTyping?: boolean;
}

interface AISuggestion {
  id: string;
  text: string;
  icon?: React.ComponentType<any>;
  action: () => void;
}

interface AITalk {
  message: string;
  quickReplies?: { text: string; action: string }[];
  mood?: 'happy' | 'excited' | 'calm' | 'curious';
}


interface WaterfallCard {
  id: string;
  type: 'creation' | 'ai_inspired' | 'resonance' | 'remix' | 'hot_comment';
  title: string;
  artist: string;
  mood: string;
  coverGradient: string;
  duration: string;
  plays: number;
  likes: number;
  
  // 创作诞生卡专属
  creationStory?: string;
  storyComments?: number;
  
  // AI灵感卡专属
  aiInspiration?: string;
  chatRounds?: number;
  
  // 情感涟漪卡专属
  resonanceLyric?: string;
  resonanceCount?: number;
  
  // 价值衍生卡专属
  remixCount?: number;
  remixStyles?: string[];
  
  // 热评卡专属
  hotComment?: string;
  commentAuthor?: string;
  commentLikes?: number;
}

// ==================== 配色系统 ====================
const moodColors: Record<string, MoodColor> = {
  happy: {
    primary: 'from-yellow-400 via-amber-400 to-orange-400',
    secondary: 'from-amber-300 to-yellow-300',
    particles: ['#FCD34D', '#FB923C', '#FBBF24', '#F59E0B'],
    glow: 'shadow-yellow-400/50',
    background: 'from-amber-50 to-orange-50',
    accent: '#F59E0B'
  },
  calm: {
    primary: 'from-blue-400 via-cyan-400 to-teal-400',
    secondary: 'from-teal-300 to-blue-300',
    particles: ['#60A5FA', '#06B6D4', '#14B8A6', '#10B981'],
    glow: 'shadow-cyan-400/50',
    background: 'from-blue-50 to-cyan-50',
    accent: '#06B6D4'
  },
  excited: {
    primary: 'from-purple-400 via-pink-400 to-rose-400',
    secondary: 'from-rose-300 to-purple-300',
    particles: ['#C084FC', '#F472B6', '#EC4899', '#DB2777'],
    glow: 'shadow-pink-400/50',
    background: 'from-purple-50 to-pink-50',
    accent: '#EC4899'
  },
  curious: {
    primary: 'from-indigo-400 via-purple-400 to-violet-400',
    secondary: 'from-violet-300 to-indigo-300',
    particles: ['#818CF8', '#A78BFA', '#C084FC', '#E879F9'],
    glow: 'shadow-purple-400/50',
    background: 'from-indigo-50 to-purple-50',
    accent: '#A78BFA'
  }
};

// ==================== AI语言库 ====================
const aiTalkLibrary: AITalk[] = [
  {
    message: "最近大家都在玩表白神曲，我们也来创作一首吗？",
    quickReplies: [
      { text: "好呀！", action: "create_love_song" },
      { text: "听听别人的", action: "browse_love_songs" }
    ],
    mood: 'excited'
  },
  {
    message: "今天心情怎么样？让我为你推荐合适的音乐～",
    quickReplies: [
      { text: "很开心", action: "mood_happy" },
      { text: "有点累", action: "mood_tired" }
    ],
    mood: 'calm'
  },
  {
    message: "发现了一首宝藏歌曲，90%的人听了都会单曲循环！",
    quickReplies: [
      { text: "快给我听", action: "play_treasure" },
      { text: "收藏起来", action: "save_treasure" }
    ],
    mood: 'happy'
  },
  {
    message: "深夜了，需要一些助眠音乐吗？",
    quickReplies: [
      { text: "来一首", action: "play_sleep" },
      { text: "我还不困", action: "browse_energetic" }
    ],
    mood: 'calm'
  }
];

// ==================== 主组件 ====================
const MusicHomePage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentMood, setCurrentMood] = useState<string>('excited');
  const [particles, setParticles] = useState<Particle[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<'home'>('home');
  const [justLongPressed, setJustLongPressed] = useState(false);

  // AI 相关状态
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiButtonPulse, setAiButtonPulse] = useState(true);



  // AI丝带显示控制（现在由AIChat组件管理）
  const [showAiRibbon, setShowAiRibbon] = useState(true);

  // 单击延迟处理（用于区分单击和双击）
  const clickTimer = useRef<NodeJS.Timeout | null>(null);

  // Refs
  const animationRef = useRef<number | undefined>(undefined);
  const aiButtonRef = useRef<HTMLDivElement>(null);

  // ========== 模拟数据生成 ==========
  const generateMusicCards = useCallback((): MusicCard[] => {
    const titles = [
      '夏日微风', '深夜独白', '清晨第一缕阳光', '雨后彩虹', '星空下的约定',
      '咖啡馆的下午', '地铁上的思念', '窗外的风景', '回忆的旋律', '未来的憧憬'
    ];

    const artists = [
      '小明的心情', '月光诗人', '都市游吟者', '梦想家小李', '音乐精灵'
    ];

    const tags = [
      ['治愈', '轻音乐'], ['深夜', 'emo'], ['励志', '早安'], ['浪漫', '爱情'],
      ['怀旧', '青春'], ['解压', '放松'], ['失眠', '助眠'], ['学习', '专注']
    ];

    // 定义一些故事专辑
    const storyAlbums = [
      { id: 'sa-1', name: '西游新编', totalEpisodes: 12, genre: 'classic' },
      { id: 'sa-2', name: '我的大学时光', totalEpisodes: 8, genre: 'personal' },
      { id: 'sa-3', name: '星际恋曲', totalEpisodes: 10, genre: 'romance' }
    ];

    const moods = Object.keys(moodColors);

    return Array.from({ length: 10 }, (_, i) => {
      const baseCard: MusicCard = {
        id: `music-${Date.now()}-${i}`,
        title: titles[i % titles.length],
        artist: artists[i % artists.length],
        mood: moods[i % moods.length],
        plays: Math.floor(Math.random() * 10000) + 100,
        likes: Math.floor(Math.random() * 5000) + 50,
        coverGradient: moodColors[moods[i % moods.length]].primary,
        duration: `${Math.floor(Math.random() * 3) + 2}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
        waveform: Array.from({ length: 30 }, () => Math.random() * 100),
        tags: tags[i % tags.length],
        createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        storyBrief: '这是一个关于' + titles[i % titles.length] + '的故事...',
        energy: Math.floor(Math.random() * 100),
        resonanceCount: Math.floor(Math.random() * 1000) + 100,
        remixCount: Math.floor(Math.random() * 50) + 5
      };

      // 随机为30%的歌曲分配故事专辑
      if (Math.random() < 0.3 && storyAlbums.length > 0) {
        const album = storyAlbums[Math.floor(Math.random() * storyAlbums.length)];
        baseCard.storyAlbum = {
          id: album.id,
          name: album.name,
          episode: Math.floor(Math.random() * album.totalEpisodes) + 1,
          totalEpisodes: album.totalEpisodes
        };
      }

      return baseCard;
    });
  }, []);

  const generateStoryCards = useCallback((): StoryCard[] => {
  // 定义故事专辑
  const storyAlbums = [
    { id: 'sa-1', name: '西游新编', totalEpisodes: 12 },
    { id: 'sa-2', name: '我的大学时光', totalEpisodes: 8 },
    { id: 'sa-3', name: '星际恋曲', totalEpisodes: 10 },
    { id: 'sa-4', name: '都市夜归人', totalEpisodes: 6 }
  ];

  const stories = [
    {
      id: 'story-1',
      title: '那个夏天的告别',
      author: '追光者',
      story: '毕业那天，我们约定十年后再见。如今音乐响起，仿佛又回到了那个充满阳光的教室...',
      musicId: 'music-1',
      coverGradient: moodColors.happy.primary,
      mood: 'happy',
      resonanceCount: 2341,
      createdAt: new Date(),
      duration: '3:24',
      coverImage: SummerFarewell,
      // 添加故事专辑信息
      storyAlbum: {
        id: 'sa-2',
        name: '我的大学时光',
        episode: 8,
        totalEpisodes: 8
      }
    },
    {
      id: 'story-2',
      title: '深夜的地铁站',
      author: '城市夜归人',
      story: '凌晨的最后一班地铁，看着窗外飞驰而过的灯光，突然想起了远方的你...',
      musicId: 'music-2',
      coverGradient: moodColors.calm.primary,
      mood: 'calm',
      resonanceCount: 1892,
      createdAt: new Date(),
      duration: '4:12',
      coverImage: MidnightSubway,
      // 添加故事专辑信息
      storyAlbum: {
        id: 'sa-4',
        name: '都市夜归人',
        episode: 3,
        totalEpisodes: 6
      }
    },
    {
      id: 'story-3',
      title: '妈妈的生日歌',
      author: '小棉袄',
      story: '第一次用AI为妈妈写歌，她听着听着就哭了，说这是最好的生日礼物...',
      musicId: 'music-3',
      coverGradient: moodColors.excited.primary,
      mood: 'excited',
      resonanceCount: 5678,
      createdAt: new Date(),
      duration: '2:58',
      coverImage: MomBirthdaySong
      // 这个故事不属于任何专辑，所以没有storyAlbum字段
    },
    {
      id: 'story-4',
      title: '大闹天宫',
      author: '说书人',
      story: '齐天大圣孙悟空，一棒打破凌霄殿。这不是你熟悉的西游记，这是用摇滚唱出的反叛...',
      musicId: 'music-4',
      coverGradient: moodColors.curious.primary,
      mood: 'curious',
      resonanceCount: 3456,
      createdAt: new Date(),
      duration: '5:12',
      coverImage: HeavenHavoc,
      // 添加故事专辑信息
      storyAlbum: {
        id: 'sa-1',
        name: '西游新编',
        episode: 2,
        totalEpisodes: 12
      }
    }
  ];

  return stories;
}, []);

  const generateChainChallenges = useCallback((): ChainChallenge[] => {
    return [
      {
        id: 'chain-1',
        title: '用5个字创作一首歌',
        participants: 1234,
        segments: 8,
        coverGradient: 'from-purple-400 to-pink-400',
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        reward: '奖励：限定音色包'
      },
      {
        id: 'chain-2',
        title: '接龙：夏日恋爱物语',
        participants: 892,
        segments: 12,
        coverGradient: 'from-orange-400 to-pink-400',
        deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        reward: '奖励：7天会员'
      }
    ];
  }, []);

  
  const generateWaterfallCards = useCallback((): WaterfallCard[] => {
    const cards: WaterfallCard[] = [
      // 创作诞生卡
      {
        id: 'wf-1',
        type: 'creation',
        title: '深夜的地铁站',
        artist: '城市夜归人',
        mood: 'calm',
        coverGradient: moodColors.calm.primary,
        duration: '3:24',
        creationStory: '凌晨两点的地铁站，看着最后一班车远去，突然想起了远方的你。这首歌记录了那个瞬间的所有情绪...',
        storyComments: 342,
        plays: 8923,
        likes: 1205
      },
      
      // 思想碰撞卡
      {
        id: 'wf-2',
        type: 'ai_inspired',
        title: '雨后彩虹',
        artist: '梦想家小李',
        mood: 'happy',
        coverGradient: moodColors.happy.primary,
        duration: '2:58',
        aiInspiration: '小弦建议："试试在副歌加入童声合唱，会让整首歌更有希望的感觉"',
        chatRounds: 23,
        plays: 5234,
        likes: 892
      },
      
      // 情感涟漪卡
      {
        id: 'wf-3',
        type: 'resonance',
        title: '妈妈的味道',
        artist: '游子心',
        mood: 'excited',
        coverGradient: moodColors.excited.primary,
        duration: '4:15',
        resonanceLyric: '离家千里，最想念的还是妈妈做的那碗蛋炒饭',
        resonanceCount: 2341,
        plays: 15234,
        likes: 3421
      },
      
      // 价值衍生卡
      {
        id: 'wf-4',
        type: 'remix',
        title: '夏日序曲',
        artist: 'DJ梦幻',
        mood: 'curious',
        coverGradient: moodColors.curious.primary,
        duration: '3:45',
        remixCount: 28,
        remixStyles: ['电音版', 'R&B版', '民谣版'],
        plays: 32145,
        likes: 8923
      },
      
      // 热评卡
      {
        id: 'wf-5',
        type: 'hot_comment',
        title: '十七岁的夏天',
        artist: '青春纪念册',
        mood: 'happy',
        coverGradient: moodColors.happy.primary,
        duration: '3:12',
        hotComment: '听着这首歌，仿佛又回到了那个充满可能的夏天，谢谢你让我重新相信梦想',
        commentAuthor: '@追光者',
        commentLikes: 5678,
        plays: 45678,
        likes: 12034
      },
      
      // 更多卡片...
      {
        id: 'wf-6',
        type: 'creation',
        title: '咖啡馆的下午',
        artist: '文艺青年',
        mood: 'calm',
        coverGradient: moodColors.calm.primary,
        duration: '2:45',
        creationStory: '在这个熟悉的角落，写下第100首歌。窗外的梧桐叶正好落下...',
        storyComments: 128,
        plays: 3456,
        likes: 567
      },
      
      {
        id: 'wf-7',
        type: 'resonance',
        title: '毕业那天',
        artist: '时光收藏家',
        mood: 'excited',
        coverGradient: moodColors.excited.primary,
        duration: '4:02',
        resonanceLyric: '我们约定，十年后的今天，还在这里相见',
        resonanceCount: 8934,
        plays: 67890,
        likes: 23456
      },
      
      {
        id: 'wf-8',
        type: 'ai_inspired',
        title: '星空下的约定',
        artist: '音乐魔法师',
        mood: 'curious',
        coverGradient: moodColors.curious.primary,
        duration: '3:33',
        aiInspiration: '小弦："加入星空音效，营造宇宙漫游的感觉"',
        chatRounds: 45,
        plays: 12345,
        likes: 4567
      }
    ];
    
    return cards;
  }, []);

  const [musicCards] = useState<MusicCard[]>(generateMusicCards());
  const [storyCards] = useState<StoryCard[]>(generateStoryCards());
  const [chainChallenges] = useState<ChainChallenge[]>(generateChainChallenges());
  const allWaterfallCards = useMemo(() => generateWaterfallCards(), [generateWaterfallCards]);

  // ========== AI相关处理 ==========
  const aiContextData = useMemo(() => ({
    isPlaying: !!currentPlayingId,
    currentSong: currentPlayingId ? musicCards.find(c => c.id === currentPlayingId) : null,
    playProgress: 60, // 模拟播放进度
    tasks: [] // 可以添加多任务
  }), [currentPlayingId, musicCards]);


  const [waterfallCardsLeft, waterfallCardsRight] = useMemo(() => {
    const left: WaterfallCard[] = [];
    const right: WaterfallCard[] = [];
    
    allWaterfallCards.forEach((card, index) => {
      if (index % 2 === 0) {
        left.push(card);
      } else {
        right.push(card);
      }
    });
  
  return [left, right];
}, [allWaterfallCards]);

  // ========== 生命周期 ==========
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);

    return () => {
      clearInterval(timer);
      if (clickTimer.current) {
        clearTimeout(clickTimer.current);
      }
    };
  }, []);

  // ========== 粒子系统 ==========
  useEffect(() => {
    const initParticles = () => {
      const newParticles: Particle[] = [];
      const colors = moodColors[currentMood].particles;
      const particleTypes: Particle['type'][] = ['note', 'star', 'heart', 'wave'];
      
      for (let i = 0; i < 20; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          vx: (Math.random() - 0.5) * 0.2,
          vy: (Math.random() - 0.5) * 0.2,
          size: Math.random() * 3 + 1,
          opacity: Math.random() * 0.15 + 0.05,
          color: colors[Math.floor(Math.random() * colors.length)],
          type: particleTypes[Math.floor(Math.random() * particleTypes.length)],
          rotation: Math.random() * 360,
          rotationSpeed: (Math.random() - 0.5) * 2,
          lifespan: 100,
          maxLife: 100
        });
      }
      setParticles(newParticles);
    };

    initParticles();
  }, [currentMood]);

  // ========== 粒子动画循环 ==========
  useEffect(() => {
    const animate = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => {
          const newX = particle.x + particle.vx;
          const newY = particle.y + particle.vy;
          let newVx = particle.vx;
          let newVy = particle.vy;
          
          if (newX <= 0 || newX >= window.innerWidth) newVx = -newVx;
          if (newY <= 0 || newY >= window.innerHeight) newVy = -newVy;
          
          return {
            ...particle,
            x: newX,
            y: newY,
            vx: newVx,
            vy: newVy,
            rotation: particle.rotation + particle.rotationSpeed,
            opacity: particle.opacity + Math.sin(Date.now() * 0.001 + particle.id) * 0.01
          };
        })
      );
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);







  const handleAiButtonClick = useCallback(() => {
    if (justLongPressed) return;

    if (clickTimer.current) {
      clearTimeout(clickTimer.current);
      clickTimer.current = null;
      return;
    }

    clickTimer.current = setTimeout(() => {
      setShowAiChat(true); // 直接打开AIChat组件
      clickTimer.current = null;
    }, 250);
  }, [justLongPressed]);

  // 双击事件处理 - 切换AI小弦丝带显示
  const handleAiButtonDoubleClick = useCallback(() => {
    // 清除单击定时器，阻止单击操作执行
    if (clickTimer.current) {
      clearTimeout(clickTimer.current);
      clickTimer.current = null;
    }

    // 切换AI小弦区显示状态
    setShowAiRibbon(prev => !prev);

    // 如果聊天窗口开着，也关闭它
    if (showAiChat) {
      setShowAiChat(false);
    }
  }, [showAiChat]);

  // ========== AI GUI 操作处理 ==========
  const handleGuiAction = useCallback((action: string, data?: any) => {
    console.log('AI触发GUI操作:', action, data);

    switch (action) {
      case 'open_full_chat':
        // 打开完整聊天界面
        setShowAiChat(true);
        break;
      case 'play_all':
        // 播放所有推荐歌曲
        if (data?.songs?.length > 0) {
          setCurrentPlayingId(data.songs[0].id);
        }
        break;
      case 'start_creation':
        // 跳转到创作页面
        console.log('跳转到创作页面');
        break;
      default:
        console.log('未处理的GUI操作:', action);
    }
  }, []);

  // ========== 音乐控制 ==========
  const handlePlayMusic = useCallback((cardId: string) => {
    setCurrentPlayingId(prev => prev === cardId ? null : cardId);
  }, []);

  // ========== 渲染粒子形状 ==========
  const renderParticleShape = (type: Particle['type']) => {
    switch (type) {
      case 'note': return '♪';
      case 'star': return '✦';
      case 'heart': return '♥';
      case 'wave': return '~';
      default: return '•';
    }
  };

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">
                  {currentTime.getHours().toString().padStart(2, '0')}:
                  {currentTime.getMinutes().toString().padStart(2, '0')}
                </span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <Bell className="w-3.5 h-3.5 text-white/70" />
                <div className="w-6 h-3 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '85%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 动态背景层 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${moodColors[currentMood].primary} opacity-10 transition-all duration-1000`} />
          
          {/* 粒子系统 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {particles.map((particle) => (
              <div
                key={particle.id}
                className="absolute animate-float-particle"
                style={{
                  left: `${particle.x}px`,
                  top: `${particle.y}px`,
                  transform: `translate(-50%, -50%) rotate(${particle.rotation}deg)`,
                  fontSize: `${particle.size * 4}px`,
                  color: particle.color,
                  opacity: particle.opacity,
                  filter: 'blur(0.5px)',
                  textShadow: `0 0 ${particle.size * 2}px ${particle.color}`
                }}
              >
                {renderParticleShape(particle.type)}
              </div>
            ))}
          </div>

          {/* AI小弦丝带 - 使用新的AIChat组件 */}
          <AIChat
            isOpen={true}
            onClose={() => {}}
            currentPage={currentPage}
            contextData={aiContextData}
            onGuiAction={handleGuiAction}
            ribbonMode={true}
            ribbonVisible={showAiRibbon}
            allowOverlay={false}
            onRibbonToggle={setShowAiRibbon}
          />

          {/* 主内容区域 - 核心优化部分 */}
          <div className={`absolute ${showAiRibbon ? 'top-[200px]' : 'top-[60px]'} bottom-[80px] left-0 right-0 overflow-hidden px-6 transition-all duration-300`}>
            {currentPage === 'home' && (
              <div className="h-full overflow-y-auto no-scrollbar">
                
                {/* 一键创作入口 - 场景化模板 */}
                <div className="mb-4">
                  <div className="grid grid-cols-4 gap-3">
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-pink-500/20 to-purple-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Heart className="w-5 h-5 text-pink-400" />
                      <span className="text-xs text-white/80">表白</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Gift className="w-5 h-5 text-yellow-400" />
                      <span className="text-xs text-white/80">生日</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Users className="w-5 h-5 text-blue-400" />
                      <span className="text-xs text-white/80">婚礼</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Moon className="w-5 h-5 text-green-400" />
                      <span className="text-xs text-white/80">助眠</span>
                    </button>
                  </div>
                  <button className="mt-3 w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium flex items-center justify-center gap-2 hover:scale-[1.02] transition-transform">
                    <Feather className="w-5 h-5" />
                    <span>一键创作属于你的歌</span>
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>

                {/* 今日故事精选 - 突出故事性 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <BookOpen className="w-5 h-5 text-amber-400" />
                      今日音乐故事精选
                    </h2>
                    <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-full">
                      每个故事都是一首歌
                    </span>
                  </div>
                  <div className="space-y-3">
                    {storyCards.map((story) => (
                      <div key={story.id} className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 hover:bg-white/10 transition-all cursor-pointer">
                        <div className="flex gap-4">
                          <div className="w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden">
                            {story.coverImage ? (
                              <img 
                                src={story.coverImage} 
                                alt={story.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  // Fallback to gradient if image fails to load
                                  const target = e.target as HTMLImageElement;
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.className = `w-16 h-16 bg-gradient-to-br ${story.coverGradient} rounded-xl flex items-center justify-center flex-shrink-0`;
                                    parent.innerHTML = '<svg class="w-8 h-8 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>';
                                  }
                                }}
                              />
                            ) : (
                              <div className={`w-full h-full bg-gradient-to-br ${story.coverGradient} rounded-xl flex items-center justify-center`}>
                                <Music className="w-8 h-8 text-white/80" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-start gap-2 mb-1">
                              <h3 className="text-white font-medium text-sm flex-1">{story.title}</h3>
                              {/* 新增：故事专辑标记 */}
                              {story.storyAlbum && (
                                <div className="flex items-center gap-1.5 bg-gradient-to-r from-purple-500/20 to-violet-500/20 backdrop-blur px-2 py-1 rounded-full">
                                  <BookOpen className="w-3 h-3 text-purple-400" />
                                  <span className="text-xs text-purple-300 font-medium">
                                    {story.storyAlbum.name}
                                  </span>
                                  <span className="text-xs text-purple-300/60">
                                    第{story.storyAlbum.episode}/{story.storyAlbum.totalEpisodes}集
                                  </span>
                                </div>
                              )}
                            </div>
                            <p className="text-white/60 text-xs line-clamp-2 mb-2">{story.story}</p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <span className="text-xs text-white/50">@{story.author}</span>
                                <div className="flex items-center gap-1">
                                  <Heart className="w-3 h-3 text-pink-400" />
                                  <span className="text-xs text-white/50">{story.resonanceCount}</span>
                                </div>
                              </div>
                              <button className="text-xs text-purple-400 hover:text-purple-300">
                                {story.storyAlbum ? '听完整故事 →' : '听Ta的故事 →'}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* 探索更多按钮 */}
                  <div className="mt-4 flex justify-center">
                    <button className="flex items-center gap-2 px-4 py-2 text-white/50 hover:text-white/70 text-sm transition-all hover:scale-105 group">
                      <span>探索更多</span>
                      <ChevronRight className="w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
                    </button>
                  </div>
                </div>

                {/* 热门榜单入口 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Trophy className="w-5 h-5 text-yellow-400" />
                      热门榜单
                    </h2>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3">
                    <button className="bg-gradient-to-br from-red-500/20 to-orange-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <Flame className="w-6 h-6 text-orange-400 mb-2" />
                      <p className="text-white font-medium text-sm">飙升榜</p>
                      <p className="text-white/50 text-xs mt-1">实时更新</p>
                    </button>
                    <button className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <Heart className="w-6 h-6 text-pink-400 mb-2" />
                      <p className="text-white font-medium text-sm">共鸣榜</p>
                      <p className="text-white/50 text-xs mt-1">最触动心弦</p>
                    </button>
                    <button className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <TrendingUp className="w-6 h-6 text-green-400 mb-2" />
                      <p className="text-white font-medium text-sm">新歌榜</p>
                      <p className="text-white/50 text-xs mt-1">发现新声音</p>
                    </button>
                  </div>
                </div>

                {/* 音乐接龙挑战 - 社交玩法 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Users className="w-5 h-5 text-blue-400" />
                      音乐接龙挑战
                    </h2>
                    <button className="text-sm text-white/60 hover:text-white">
                      更多 <ChevronRight className="inline w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    {chainChallenges.map((challenge) => (
                      <div key={challenge.id} className="bg-white/5 backdrop-blur-lg rounded-xl p-3 hover:bg-white/10 transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-12 h-12 bg-gradient-to-br ${challenge.coverGradient} rounded-lg flex items-center justify-center`}>
                              <Hash className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="text-white font-medium text-sm">{challenge.title}</p>
                              <div className="flex items-center gap-3 mt-1">
                                <span className="text-xs text-white/50">{challenge.participants}人参与</span>
                                <span className="text-xs text-white/50">{challenge.segments}段接龙</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-purple-400 font-medium">{challenge.reward}</p>
                            <p className="text-xs text-white/40 mt-1">
                              还剩{Math.floor((challenge.deadline.getTime() - Date.now()) / (24 * 60 * 60 * 1000))}天
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
  
                {/* 情感共鸣广场 - 分类音乐流 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-purple-400" />
                      情感共鸣广场
                    </h2>
                  </div>
                  
                  <div className="flex gap-3 overflow-x-auto no-scrollbar pb-2">
                    <button className="flex-shrink-0 px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur rounded-full text-white text-sm font-medium flex items-center gap-2">
                      <span>💔</span> 失恋疗伤
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">234在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>📚</span> 考研加油
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">189在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>💼</span> 职场解压
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">156在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>💕</span> 表白墙
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">423在听</span>
                    </button>
                  </div>
                </div>

              {/* 社区精选瀑布流 - 充满故事的音乐世界 */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                    <Layers className="w-5 h-5 text-gradient-to-r from-purple-400 to-pink-400" />
                    社区精选
                    <span className="ml-2 text-xs text-white/40 font-normal">每首歌都有故事</span>
                  </h2>
                </div>
                
                {/* 瀑布流容器 */}
                <div className="grid grid-cols-2 gap-3">
                  {/* 左列 */}
                  <div className="space-y-3">
                    {waterfallCardsLeft.map((card) => (
                      <WaterfallCard key={card.id} card={card} onPlay={handlePlayMusic} />
                    ))}
                  </div>
                  
                  {/* 右列 */}
                  <div className="space-y-3">
                    {waterfallCardsRight.map((card) => (
                      <WaterfallCard key={card.id} card={card} onPlay={handlePlayMusic} />
                    ))}
                  </div>
                </div>
              </div>

                
              </div>
            )}
          </div>



          {/* 底部导航栏 */}
          <div className="absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-xl border-t border-white/10">
            <div className="flex items-center justify-between px-6 py-3">
              <button 
                className={`flex flex-col items-center gap-1 py-1 transition-all ${
                  currentPage === 'home' ? 'text-white' : 'text-white/50'
                }`}
                onClick={() => setCurrentPage('home')}
              >
                <Home className="w-5 h-5" />
                <span className="text-xs">首页</span>
              </button>

              <div className="flex flex-col items-center gap-1 py-1 text-white/50">
                <PenTool className="w-5 h-5" />
                <span className="text-xs">创作</span>
              </div>

              <div className="flex flex-col items-center gap-1 py-1 text-white/50">
                <User className="w-5 h-5" />
                <span className="text-xs">我的</span>
              </div>

              <div className="relative">
                <div
                  ref={aiButtonRef}
                  className="relative transition-all duration-300"
                  onClick={handleAiButtonClick}
                  onDoubleClick={handleAiButtonDoubleClick}
                >
                  {aiButtonPulse && (
                    <div className="absolute -inset-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                  )}
                  
                  <div className="relative w-14 h-14 rounded-full overflow-hidden cursor-pointer bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 shadow-2xl transform transition-all hover:scale-105">
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-full h-full animate-flow"
                          style={{
                            background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                            animationDelay: `${i * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="text-white text-2xl font-bold">✧</div>
                      <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 播放控制条 */}
          {currentPlayingId && (
            <div className="absolute bottom-20 left-4 right-4 bg-black/80 backdrop-blur-xl rounded-2xl p-3 z-30 animate-slide-up">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-white text-sm font-medium">
                    {musicCards.find(c => c.id === currentPlayingId)?.title}
                  </p>
                  <p className="text-white/60 text-xs">
                    {musicCards.find(c => c.id === currentPlayingId)?.artist}
                  </p>
                </div>
                <button 
                  className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center"
                  onClick={() => handlePlayMusic(currentPlayingId)}
                >
                  <Pause className="w-5 h-5 text-white" />
                </button>
                <button className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                  <SkipForward className="w-5 h-5 text-white" />
                </button>
              </div>
              <div className="mt-2">
                <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-progress" />
                </div>
              </div>
            </div>
          )}

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
        {/* 添加AIChat组件 */}
        <AIChat 
          isOpen={showAiChat}
          onClose={() => setShowAiChat(false)}
          currentPage="home"
          contextData={aiContextData}
          onGuiAction={handleGuiAction}
        />
      </div>
    </div>
  );
};

// ==================== 子组件 ====================

// 音乐卡片组件
const MusicCard: React.FC<{
  card: MusicCard;
  isPlaying: boolean;
  onPlay: () => void;
}> = ({ card, isPlaying, onPlay }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className="relative cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onPlay}
    >
      {isPlaying && (
        <div className="absolute -inset-2 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-2xl animate-pulse" />
      )}
      
      <div className={`relative bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden transition-all duration-300 ${
        isHovered ? 'transform scale-105' : ''
      }`}>
        <div className={`absolute inset-0 bg-gradient-to-br ${card.coverGradient} opacity-30`} />
        
        {isPlaying && card.waveform && (
          <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around h-20 px-2">
            {card.waveform.slice(0, 10).map((height, i) => (
              <div
                key={i}
                className="w-1 bg-white/40 rounded-full animate-wave"
                style={{
                  height: `${height * 0.5}%`,
                  animationDelay: `${i * 100}ms`
                }}
              />
            ))}
          </div>
        )}
        
        <div className="relative p-4">
          {/* 新增：故事专辑角标 */}
          {card.storyAlbum && (
            <div className="absolute top-2 right-2 z-10">
              <div className="relative group/story">
                <div className="w-7 h-7 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center shadow-lg">
                  <BookOpen className="w-4 h-4 text-white" />
                </div>
                {/* 悬停提示 */}
                <div className="absolute right-0 top-8 bg-black/90 backdrop-blur text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap opacity-0 group-hover/story:opacity-100 transition-opacity pointer-events-none">
                  {card.storyAlbum.name} · 第{card.storyAlbum.episode}集
                </div>
              </div>
            </div>
          )}

          <div className="w-full aspect-square bg-gradient-to-br from-white/20 to-white/5 rounded-xl mb-3 flex items-center justify-center">
            <div className={`relative ${isPlaying ? 'animate-spin-slow' : ''}`}>
              <Music className="w-12 h-12 text-white/80" />
            </div>
          </div>

          <h3 className="text-white font-medium text-sm truncate">{card.title}</h3>
          <p className="text-white/60 text-xs truncate">{card.artist}</p>

          <div className="flex gap-1 mt-2">
            {card.tags.slice(0, 2).map((tag, i) => (
              <span key={i} className="text-xs bg-white/10 text-white/70 px-2 py-0.5 rounded-full">
                {tag}
              </span>
            ))}
          </div>
        </div>
        
        <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
            {isPlaying ? (
              <Pause className="w-6 h-6 text-gray-800" />
            ) : (
              <Play className="w-6 h-6 text-gray-800 ml-1" />
            )}
          </div>
        </div>
      </div>
      

    </div>
  );
};

// 音乐卡片组件瀑布流卡片组件
const WaterfallCard: React.FC<{
  card: WaterfallCard;
  onPlay: (id: string) => void;
}> = ({ card, onPlay }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  
  return (
    <div 
      className="relative bg-white/5 backdrop-blur-lg rounded-2xl overflow-hidden hover:bg-white/10 transition-all cursor-pointer group"
      onClick={() => {
        onPlay(card.id);
        setIsPlaying(!isPlaying);
      }}
    >
      {/* 封面区域 */}
      <div className="relative">
        <div className={`w-full ${card.type === 'resonance' || card.type === 'hot_comment' ? 'h-32' : 'h-28'} bg-gradient-to-br ${card.coverGradient} relative overflow-hidden`}>
          {/* 播放状态动画 */}
          {isPlaying && (
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <div className="w-12 h-12 bg-white/20 backdrop-blur rounded-full flex items-center justify-center animate-pulse">
                <Music className="w-6 h-6 text-white animate-spin-slow" />
              </div>
            </div>
          )}
          
          {/* 播放量标签 */}
          <div className="absolute top-2 left-2 bg-black/40 backdrop-blur px-2 py-0.5 rounded-full flex items-center gap-1">
            <Play className="w-3 h-3 text-white/80" />
            <span className="text-xs text-white/80">{(card.plays / 1000).toFixed(1)}k</span>
          </div>
          
          {/* 时长标签 */}
          <div className="absolute top-2 right-2 bg-black/40 backdrop-blur px-2 py-0.5 rounded-full">
            <span className="text-xs text-white/80">{card.duration}</span>
          </div>
          
          {/* 悬停播放按钮 */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <div className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center transform scale-0 group-hover:scale-100 transition-transform">
              <Play className="w-5 h-5 text-gray-800 ml-0.5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="p-3">
        {/* 标题和作者 */}
        <h3 className="text-white font-medium text-sm mb-0.5 line-clamp-1">{card.title}</h3>
        <p className="text-white/50 text-xs mb-2">@{card.artist}</p>
        
        {/* 特色内容区域 - 根据卡片类型展示不同内容 */}
        {card.type === 'creation' && (
          <div className="bg-white/5 rounded-lg p-2.5 mb-2">
            <div className="flex items-start gap-1.5 mb-2">
              <Feather className="w-3.5 h-3.5 text-amber-400 mt-0.5 flex-shrink-0" />
              <p className="text-white/70 text-xs leading-relaxed line-clamp-3">
                {card.creationStory}
              </p>
            </div>
            <div className="flex items-center gap-1 text-white/50">
              <MessageCircle className="w-3 h-3" />
              <span className="text-xs">{card.storyComments}条故事评论</span>
            </div>
          </div>
        )}
        
        {card.type === 'ai_inspired' && (
          <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg p-2.5 mb-2 border border-purple-500/20">
            <div className="flex items-start gap-1.5 mb-2">
              <div className="w-4 h-4 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center flex-shrink-0">
                <Sparkles className="w-2.5 h-2.5 text-white" />
              </div>
              <p className="text-purple-300 text-xs leading-relaxed line-clamp-2">
                {card.aiInspiration}
              </p>
            </div>
            <div className="flex items-center gap-1 text-purple-400/70">
              <Brain className="w-3 h-3" />
              <span className="text-xs">{card.chatRounds}轮灵感碰撞</span>
            </div>
          </div>
        )}
        
        {card.type === 'resonance' && (
          <div className="bg-gradient-to-br from-pink-500/10 to-rose-500/10 rounded-lg p-2.5 mb-2">
            <div className="flex items-start gap-1.5 mb-2">
              <span className="text-pink-400 text-base leading-none mt-0.5">"</span>
              <p className="text-white/80 text-xs leading-relaxed font-medium italic">
                {card.resonanceLyric}
              </p>
              <span className="text-pink-400 text-base leading-none mt-0.5 self-end">"</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Heart className="w-3.5 h-3.5 text-pink-400 fill-pink-400" />
                <span className="text-xs text-pink-400 font-medium">{card.resonanceCount}人共鸣</span>
              </div>
              <div className="flex -space-x-1.5">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="w-5 h-5 rounded-full bg-gradient-to-br from-pink-400 to-rose-400 border border-white/20" />
                ))}
                <div className="w-5 h-5 rounded-full bg-white/10 border border-white/20 flex items-center justify-center">
                  <span className="text-[8px] text-white/60">+{card.resonanceCount! - 4}</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {card.type === 'remix' && (
          <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg p-2.5 mb-2">
            <div className="flex items-center gap-2 mb-2">
              <Layers className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-xs font-medium">{card.remixCount}个二创版本</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {card.remixStyles?.map((style, i) => (
                <span key={i} className="text-[10px] bg-green-500/20 text-green-300 px-2 py-0.5 rounded-full">
                  {style}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {card.type === 'hot_comment' && (
          <div className="bg-gradient-to-br from-amber-500/10 to-orange-500/10 rounded-lg p-2.5 mb-2">
            <div className="mb-2">
              <p className="text-white/80 text-xs leading-relaxed">
                "{card.hotComment}"
              </p>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 rounded-full bg-gradient-to-br from-amber-400 to-orange-400" />
                <span className="text-xs text-white/60">{card.commentAuthor}</span>
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-3 h-3 text-amber-400" />
                <span className="text-xs text-amber-400">{card.commentLikes}</span>
              </div>
            </div>
          </div>
        )}
        
        {/* 底部操作栏 */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-3">
            <button className="flex items-center gap-1 hover:scale-105 transition-transform">
              <Heart className="w-3.5 h-3.5 text-white/50 hover:text-pink-400 transition-colors" />
              <span className="text-xs text-white/50">{card.likes}</span>
            </button>
            <button className="flex items-center gap-1 hover:scale-105 transition-transform">
              <MessageCircle className="w-3.5 h-3.5 text-white/50 hover:text-blue-400 transition-colors" />
            </button>
            <button className="flex items-center gap-1 hover:scale-105 transition-transform">
              <Share2 className="w-3.5 h-3.5 text-white/50 hover:text-green-400 transition-colors" />
            </button>
          </div>
          <button className="text-[10px] text-purple-400 hover:text-purple-300">
            查看详情 →
          </button>
        </div>
      </div>
      
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  @keyframes float-particle {
    0%, 100% { 
      transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
    }
    50% { 
      transform: translate(-50%, -50%) translateY(-10px) rotate(180deg);
    }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slide-up {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes scale-up {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @keyframes wave {
    0%, 100% { height: 20%; }
    50% { height: 60%; }
  }
  
  @keyframes progress {
    from { width: 0%; }
    to { width: 60%; }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-particle {
    animation: float-particle 8s ease-in-out infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .animate-spin-slow {
    animation: spin-slow 4s linear infinite;
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.5s ease-out;
  }
  
  .animate-scale-up {
    animation: scale-up 0.3s ease-out;
  }
  
  .animate-wave {
    animation: wave 1.5s ease-in-out infinite;
  }
  
  .animate-progress {
    animation: progress 3s ease-out;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animation-delay-100 {
    animation-delay: 100ms;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  @keyframes scroll-left {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
  }

  .animate-scroll-left {
    animation: scroll-left 30s linear infinite;
  }
`;
document.head.appendChild(style);

export default MusicHomePage;
